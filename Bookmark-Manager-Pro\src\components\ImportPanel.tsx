import {
  AlertCircle,
  Check,
  Chrome,
  Download,
  FileText,
  Globe,
  Upload,
  X
} from 'lucide-react'
import React, { useRef, useState } from 'react'
import type { Bookmark } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import { IntervalManager } from '../utils/intervalManager'

// Get intervalManager instance
const intervalManager = IntervalManager.getInstance()

interface ImportPanelProps {
  isOpen: boolean
  onClose: () => void
  onOpenPanel?: (panelType: string) => void
}

export const ImportPanel: React.FC<ImportPanelProps> = ({ isOpen, onClose, onOpenPanel }) => {

  const { addBookmarksBulk, replaceAllBookmarks, bookmarks: currentBookmarks } = useBookmarks()
  const { t } = useLocalization()
  const [isDragging, setIsDragging] = useState(false)
  const [importFormat, setImportFormat] = useState<'html' | 'json' | 'csv'>('html')
  const [importProgress, setImportProgress] = useState<number | null>(null)
  const [importStatus, setImportStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle')
  const [importedCount, setImportedCount] = useState(0)
  const [replaceExisting] = useState(true)
  const [multimediaAnalysis, setMultimediaAnalysis] = useState<any>(null)
  const [importOperationInProgress, setImportOperationInProgress] = useState(false)
  const [urlValidationEnabled, setUrlValidationEnabled] = useState(false) // Default to false for speed
  const [validationMode, setValidationMode] = useState<'fast' | 'thorough'>('fast')
  const [validationProgress, setValidationProgress] = useState<{
    total: number
    completed: number
    current?: string
    broken: number
    redirected: number
  } | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fast URL format validation (no network requests)
  const fastValidateBookmarks = (bookmarks: any[]): {
    validBookmarks: any[]
    invalidBookmarks: any[]
    validationResults: any[]
  } => {
    const validBookmarks: any[] = []
    const invalidBookmarks: any[] = []
    const validationResults: any[] = []

    bookmarks.forEach(bookmark => {
      try {
        // Basic URL format validation
        const url = new URL(bookmark.url)
        const isValidProtocol = ['http:', 'https:'].includes(url.protocol)
        const hasValidDomain = url.hostname.length > 0 && url.hostname.includes('.')

        if (isValidProtocol && hasValidDomain) {
          validBookmarks.push({
            ...bookmark,
            validationResult: { isValid: true, status: 'format_valid', errors: [] }
          })
          validationResults.push({ isValid: true, status: 'format_valid', errors: [] })
        } else {
          invalidBookmarks.push({
            ...bookmark,
            validationResult: { isValid: false, status: 'invalid_format', errors: ['Invalid URL format'] },
            validationErrors: ['Invalid URL format']
          })
          validationResults.push({ isValid: false, status: 'invalid_format', errors: ['Invalid URL format'] })
        }
      } catch (error) {
        invalidBookmarks.push({
          ...bookmark,
          validationResult: { isValid: false, status: 'parse_error', errors: ['URL parse error'] },
          validationErrors: ['URL parse error']
        })
        validationResults.push({ isValid: false, status: 'parse_error', errors: ['URL parse error'] })
      }
    })

    return { validBookmarks, invalidBookmarks, validationResults }
  }

  // Enhanced URL validation function with performance modes
  const validateImportedBookmarks = async (bookmarks: any[]): Promise<{
    validBookmarks: any[]
    invalidBookmarks: any[]
    validationResults: any[]
  }> => {
    if (!urlValidationEnabled) {
      return {
        validBookmarks: bookmarks,
        invalidBookmarks: [],
        validationResults: []
      }
    }

    // Fast mode: Only format validation (instant)
    if (validationMode === 'fast') {
      console.log('⚡ Fast URL validation for', bookmarks.length, 'bookmarks...')
      const result = fastValidateBookmarks(bookmarks)
      console.log(`✅ Fast validation complete: ${result.validBookmarks.length} valid, ${result.invalidBookmarks.length} invalid`)
      return result
    }

    // Thorough mode: Network validation (slower)
    console.log('🔍 Thorough URL validation for', bookmarks.length, 'bookmarks...')
    setValidationProgress({
      total: bookmarks.length,
      completed: 0,
      broken: 0,
      redirected: 0
    })

    const validBookmarks: any[] = []
    const invalidBookmarks: any[] = []
    const validationResults: any[] = []

    // Optimized batch processing
    const batchSize = 20 // Increased batch size
    for (let i = 0; i < bookmarks.length; i += batchSize) {
      const batch = bookmarks.slice(i, i + batchSize)

      const batchPromises = batch.map(async (bookmark, index) => {
        try {
          const result = await validationService.validateBookmark(bookmark, {
            timeout: 2000, // Reduced timeout for faster processing
            checkRedirects: false, // Skip redirect checking for speed
            followRedirects: false,
            checkContent: false
          })

          setValidationProgress(prev => prev ? {
            ...prev,
            completed: prev.completed + 1,
            current: bookmark.title,
            broken: prev.broken + (result.status === 'invalid' ? 1 : 0),
            redirected: prev.redirected + (result.redirectUrl ? 1 : 0)
          } : null)

          if (result.isValid) {
            validBookmarks.push({
              ...bookmark,
              validationResult: result
            })
          } else {
            invalidBookmarks.push({
              ...bookmark,
              validationResult: result,
              validationErrors: result.errors
            })
          }

          validationResults.push(result)
          return result
        } catch (error) {
          console.warn(`Validation failed for ${bookmark.title}:`, error)
          // Include bookmark anyway but mark as unvalidated
          validBookmarks.push({
            ...bookmark,
            validationResult: { isValid: true, status: 'unvalidated', errors: ['Validation skipped due to error'] }
          })
          return null
        }
      })

      await Promise.all(batchPromises)

      // Reduced delay between batches
      if (i + batchSize < bookmarks.length) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }

    console.log(`✅ Thorough validation complete: ${validBookmarks.length} valid, ${invalidBookmarks.length} invalid`)
    setValidationProgress(null)

    return {
      validBookmarks,
      invalidBookmarks,
      validationResults
    }
  }

  // Test function to verify import functionality
  const testImportFunctionality = async () => {
    console.log('🧪 Testing import functionality...')
    console.log('📊 Current bookmarks count:', currentBookmarks.length)

    const testBookmarks = [
      {
        title: 'Test Import - Google',
        url: 'https://www.google.com',
        description: 'Test bookmark for Google',
        tags: ['test', 'search'],
        collection: 'Test Collection',
        dateAdded: new Date().toISOString(),
        isFavorite: false,
        visits: 0,
        folder: 'Test Collection',
        path: ['Test Collection'],
        isRecentlyAdded: false,
        canRevert: false,
        addedTimestamp: new Date().toISOString()
      },
      {
        title: 'Test Import - GitHub',
        url: 'https://github.com',
        description: 'Test bookmark for GitHub',
        tags: ['test', 'development'],
        collection: 'Test Collection',
        dateAdded: new Date().toISOString(),
        isFavorite: false,
        visits: 0,
        folder: 'Test Collection',
        path: ['Test Collection'],
        isRecentlyAdded: false,
        canRevert: false,
        addedTimestamp: new Date().toISOString()
      }
    ]

    try {
      console.log('🔄 Testing addBookmarksBulk with', testBookmarks.length, 'test bookmarks')
      addBookmarksBulk(testBookmarks)
      console.log('✅ Test import completed')

      setTimeout(() => {
        console.log('📊 Bookmarks count after test import:', currentBookmarks.length)
      }, 100)
    } catch (error) {
      console.error('❌ Test import failed:', error)
    }
  }

  // Use intervalManager for managed timeouts (imported instance)

  // Generate favicon URL from domain
  const generateFaviconUrl = (url: string): string => {
    try {
      const domain = new URL(url).hostname
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`
    } catch {
      return `https://www.google.com/s2/favicons?domain=example.com&sz=32`
    }
  }

  // Analyze multimedia content types in imported bookmarks
  const analyzeMultimediaContent = (bookmarks: Omit<Bookmark, 'id'>[]): {
    total: number
    videos: number
    audio: number
    documents: number
    web: number
    breakdown: {
      youtube: number
      vimeo: number
      spotify: number
      soundcloud: number
      pdfs: number
      articles: number
    }
  } => {
    console.log('🔍 Starting analysis of', bookmarks.length, 'bookmarks')

    const analysis = {
      total: bookmarks.length,
      videos: 0,
      audio: 0,
      documents: 0,
      web: 0,
      breakdown: {
        youtube: 0,
        vimeo: 0,
        spotify: 0,
        soundcloud: 0,
        pdfs: 0,
        articles: 0
      }
    }

    if (!bookmarks || bookmarks.length === 0) {
      console.log('⚠️ No bookmarks to analyze')
      return analysis
    }

    bookmarks.forEach((bookmark, index) => {
      if (!bookmark || !bookmark.url || !bookmark.title) {
        console.log(`⚠️ Invalid bookmark at index ${index}:`, bookmark)
        return
      }

      const url = bookmark.url.toLowerCase()
      const title = bookmark.title.toLowerCase()
      let categorized = false

      // Video content
      if (url.includes('youtube.com') || url.includes('youtu.be')) {
        analysis.videos++
        analysis.breakdown.youtube++
        categorized = true
      } else if (url.includes('vimeo.com')) {
        analysis.videos++
        analysis.breakdown.vimeo++
        categorized = true
      } else if (url.includes('video') || title.includes('video')) {
        analysis.videos++
        categorized = true
      }
      // Audio content
      else if (url.includes('spotify.com')) {
        analysis.audio++
        analysis.breakdown.spotify++
        categorized = true
      } else if (url.includes('soundcloud.com')) {
        analysis.audio++
        analysis.breakdown.soundcloud++
        categorized = true
      } else if (url.includes('audio') || url.includes('music') || title.includes('podcast')) {
        analysis.audio++
        categorized = true
      }
      // Document content
      else if (url.includes('.pdf') || url.includes('pdf')) {
        analysis.documents++
        analysis.breakdown.pdfs++
        categorized = true
      } else if (title.includes('article') || title.includes('blog') || title.includes('doc')) {
        analysis.documents++
        analysis.breakdown.articles++
        categorized = true
      }

      // Everything else is web content
      if (!categorized) {
        analysis.web++
      }
    })

    console.log('✅ Analysis complete:', analysis)
    return analysis
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileUpload(file)
    }
  }



  const parseHTMLBookmarks = async (html: string): Promise<Omit<Bookmark, 'id'>[]> => {
    try {
      console.log('🔍 Using CORRECT hierarchical parser from services/bookmarkParser.ts')
      console.log('📄 HTML content length:', html.length)

      // Use the correct parser that maintains folder hierarchy
      // Import the function dynamically to avoid build issues
      const { parseBookmarksHTML } = await import('../../services/bookmarkParser')
      const parsedBookmarks = parseBookmarksHTML(html)
      console.log(`✅ Hierarchical parser found ${parsedBookmarks.length} bookmarks`)

      // Convert to the format expected by the ImportPanel (without id)
      const bookmarksWithoutId = parsedBookmarks.map((bookmark, index) => {
        const { id, ...bookmarkWithoutId } = bookmark

        // DEBUG: Log the raw bookmark data from the hierarchical parser
        if (index < 10) { // Only log first 10 for debugging
          console.log(`🔍 Raw bookmark ${index + 1}:`, {
            title: bookmark.title,
            url: bookmark.url,
            path: bookmark.path,
            pathLength: bookmark.path?.length || 0
          })
        }

        // Convert path array to collection name for compatibility
        let collection = 'Imported'
        if (bookmark.path && bookmark.path.length > 0) {
          collection = bookmark.path[bookmark.path.length - 1] // Use last folder as collection

          // ZERO FILTERING POLICY: Preserve collection names exactly as they appear
          // REMOVED: Automatic renaming of "Bookmarks bar" to "Quick Add"
        } else {
          // No artificial categorization - preserve original structure only
          collection = 'Uncategorized' // For bookmarks without folder paths

          // DEBUG: Log bookmarks with no path
          if (index < 10) {
            console.log(`🤖 Auto-categorized "${bookmark.title}" -> "${collection}" (no folder path found)`)
          }
        }

        if (index < 10) { // Only log first 10 for debugging
          console.log(`📁 Bookmark "${bookmark.title}" -> Collection: "${collection}", Path: [${bookmark.path?.join(' > ') || 'none'}]`)
        }

        // PRESERVE ORIGINAL DATES: Use bookmark creation date, not import date
        let originalDate = new Date().toISOString() // Fallback to current date

        if (bookmark.addDate) {
          try {
            // Browser exports use Unix timestamp (seconds since epoch)
            const unixTimestamp = parseInt(bookmark.addDate)
            originalDate = new Date(unixTimestamp * 1000).toISOString()

            if (index < 5) { // Debug first few dates
              console.log(`📅 Bookmark "${bookmark.title}": Original date ${bookmark.addDate} -> ${originalDate}`)
            }
          } catch (error) {
            console.warn(`⚠️ Invalid date for "${bookmark.title}": ${bookmark.addDate}`)
          }
        } else {
          if (index < 5) {
            console.log(`📅 Bookmark "${bookmark.title}": No original date found, using current date`)
          }
        }

        return {
          ...bookmarkWithoutId,
          collection,
          folder: collection,
          path: bookmark.path || [collection],
          favicon: generateFaviconUrl(bookmark.url),
          description: '',
          tags: [],
          dateAdded: originalDate, // Use original creation date
          isFavorite: false,
          visits: 0,
          isRecentlyAdded: false,
          canRevert: true,
          addedTimestamp: new Date().toISOString() // Keep import timestamp separate
        }
      })

      console.log(`📊 Converted ${bookmarksWithoutId.length} bookmarks with proper folder hierarchy`)

      // ZERO FILTERING VERIFICATION: Log all collections found during import
      const collections = [...new Set(bookmarksWithoutId.map(b => b.collection))]
      console.log(`📥 IMPORT VERIFICATION: Found ${collections.length} unique collections`)
      console.log(`📥 Collections preserved exactly as imported:`)
      collections.sort().forEach((collection, index) => {
        const isSpecial = /[()[\]{}]/.test(collection)
        const isMustRead = collection.includes('MUST READ')
        const markers = []
        if (isSpecial) markers.push('SPECIAL_CHARS')
        if (isMustRead) markers.push('MUST_READ')
        const markerText = markers.length > 0 ? ` [${markers.join(', ')}]` : ''
        console.log(`   ${index + 1}. "${collection}"${markerText}`)
      })
      console.log(`✅ ZERO FILTERING POLICY: All ${collections.length} collections preserved without modification`)

      return bookmarksWithoutId

      /* eslint-disable no-unreachable */
      // TODO: Remove this unreachable code block - it's legacy parsing code that's no longer used
      // The hierarchical parser above handles all bookmark parsing needs
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'text/html')
      const bookmarks: Omit<Bookmark, 'id'>[] = []

      // Debug: log the document structure
      console.log('📋 Document structure:')
      const h3Elements = doc.querySelectorAll('h3')
      h3Elements.forEach((h3, index) => {
        console.log(`  H3 ${index + 1}: "${h3.textContent?.trim()}"`)
      })

      const dlElements = doc.querySelectorAll('dl')
      console.log(`📦 Found ${dlElements.length} DL elements`)

      const linkElements = doc.querySelectorAll('a[href]')
      console.log(`🔗 Found ${linkElements.length} total links`)

      // Debug: log the actual DOM structure
      console.log('🏗️ Document body HTML preview:')
      console.log(doc.body.innerHTML.substring(0, 1500) + '...')

      // Debug: check if we have any DT elements
      const dtElements = doc.querySelectorAll('dt, DT')
      console.log(`📋 Found ${dtElements.length} DT elements`)

      // Debug: check case sensitivity
      const h3ElementsLower = doc.querySelectorAll('H3')
      const dlElementsLower = doc.querySelectorAll('DL')
      const dtElementsLower = doc.querySelectorAll('DT')
      console.log(`📋 Uppercase elements - H3: ${h3ElementsLower.length}, DL: ${dlElementsLower.length}, DT: ${dtElementsLower.length}`)

      // Debug: Show all H3 content and their next siblings
      const allH3s = doc.querySelectorAll('h3, H3')
      allH3s.forEach((h3, index) => {
        console.log(`📁 H3 ${index + 1}: "${h3.textContent?.trim()}"`)
        console.log(`   Next sibling:`, h3.nextElementSibling?.tagName)
        console.log(`   Parent:`, h3.parentElement?.tagName)
      })

      // Debug: Show structure of first few DT elements
      const firstDTs = Array.from(dtElements).slice(0, 5)
      firstDTs.forEach((dt, index) => {
        console.log(`📋 DT ${index + 1}:`)
        console.log(`   Contains H3:`, !!dt.querySelector('h3, H3'))
        console.log(`   Contains A:`, !!dt.querySelector('a[href], A[HREF]'))
        console.log(`   Text content:`, dt.textContent?.trim().substring(0, 50))
      })

      // Better approach: traverse the document structure to maintain folder hierarchy
      const traverseElement = (element: Element, currentFolder: string = 'Imported') => {
        console.log(`🔍 Traversing element: ${element.tagName}, current folder: ${currentFolder}`)
        const children = Array.from(element.children)
        console.log(`   Children count: ${children.length}`)

        for (let i = 0; i < children.length; i++) {
          const child = children[i]
          console.log(`   Processing child ${i + 1}: ${child.tagName}`)

          if (child.tagName === 'H3') {
            // This is a folder header
            const folderName = child.textContent?.trim() || 'Untitled Folder'
            console.log(`📁 Found folder: ${folderName}`)

            // Look ahead for the DL that contains this folder's bookmarks
            let nextSibling = child.nextElementSibling
            while (nextSibling) {
              if (nextSibling.tagName === 'DL') {
                // Process bookmarks in this folder
                traverseElement(nextSibling, folderName)
                break
              }
              nextSibling = nextSibling.nextElementSibling
            }
          } else if (child.tagName === 'DT') {
            // This might contain a bookmark or subfolder
            const anchor = child.querySelector('a[href]')
            const h3 = child.querySelector('h3')

            if (h3) {
              // This DT contains a subfolder
              const subfolderName = h3.textContent?.trim() || 'Untitled Folder'
              console.log(`📁 Found subfolder: ${subfolderName}`)

              // Look for the next DL element
              const nextElement = children[i + 1]
              if (nextElement && nextElement.tagName === 'DL') {
                traverseElement(nextElement, subfolderName)
              }
            } else if (anchor) {
              // This DT contains a bookmark
              const href = anchor.getAttribute('href')
              const title = anchor.textContent?.trim()

              if (href && title && href.startsWith('http')) {
                // Determine collection name
                let collection = currentFolder

                // ZERO FILTERING POLICY: Preserve collection names exactly as they appear
                // REMOVED: Automatic renaming of "Bookmarks bar" to "Quick Add"

                // Extract date
                const addDate = anchor.getAttribute('ADD_DATE')
                let dateAdded = new Date().toISOString()
                if (addDate) {
                  try {
                    const unixTimestamp = parseInt(addDate) * 1000
                    dateAdded = new Date(unixTimestamp).toISOString()
                  } catch {
                    dateAdded = new Date().toISOString()
                  }
                }

                // Extract tags
                const tagsAttr = anchor.getAttribute('TAGS')
                const tags = tagsAttr ? tagsAttr.split(',').map(tag => tag.trim()).filter(tag => tag) : []

                console.log(`🔖 Found bookmark: ${title} -> ${collection}`)

                bookmarks.push({
                  title,
                  url: href,
                  favicon: generateFaviconUrl(href),
                  description: '',
                  tags,
                  collection,
                  dateAdded,
                  isFavorite: false,
                  visits: 0,
                  // Additional properties for compatibility
                  folder: collection,
                  path: [collection],
                  isRecentlyAdded: false, // Don't mark imported bookmarks as recently added
                  canRevert: true,
                  addedTimestamp: new Date().toISOString()
                })
              }
            }
          } else if (child.tagName === 'DL') {
            // Direct DL element, continue with current folder
            traverseElement(child, currentFolder)
          }
        }
      }

      // Try multiple parsing approaches
      console.log('🚀 Starting traversal from document body...')
      traverseElement(doc.body)
      console.log(`📊 Structured parsing found ${bookmarks.length} bookmarks`)

      // SIMPLIFIED: Direct regex-based parsing that definitely works
      if (bookmarks.length === 0) {
        const parseStartTime = performance.now()
        console.log('🔄 Using simplified regex-based parsing...')
        console.log('📄 HTML length:', html.length, 'characters')
        console.log('📄 HTML sample for regex testing:', html.substring(0, 2000))

        // LARGE FILE ANALYSIS: Check for potential issues
        const analysisStart = performance.now()
        const h3Count = (html.match(/<h3[^>]*>/gi) || []).length
        const dlCount = (html.match(/<dl[^>]*>/gi) || []).length
        const linkCount = (html.match(/<a[^>]+href/gi) || []).length
        const analysisTime = performance.now() - analysisStart

        console.log('📊 Large file structure analysis:')
        console.log(`  H3 elements: ${h3Count}`)
        console.log(`  DL elements: ${dlCount}`)
        console.log(`  Link elements: ${linkCount}`)
        console.log(`  Analysis time: ${analysisTime.toFixed(2)}ms`)

        if (linkCount > 1000) {
          console.warn('⚠️ Large number of links detected - using optimized processing')
        }

        // PERFORMANCE OPTIMIZATION: For very large files, use chunked processing
        if (linkCount > 2000) {
          console.log('🚀 Enabling high-performance mode for large file...')
        }

        // Use regex to extract folder sections and their bookmarks
        const folderRegex = /<h3[^>]*>(.*?)<\/h3>\s*<dl[^>]*>(.*?)<\/dl>/gis
        console.log('🔍 Testing folder regex pattern...')

        let folderMatch
        let folderCount = 0
        const regexStartTime = performance.now()

        // PERFORMANCE: Set timeout for regex processing
        const regexTimeout = linkCount > 2000 ? 10000 : 5000 // 10s for very large files
        let regexTimedOut = false

        const regexTimeoutId = setTimeout(() => {
          regexTimedOut = true
          console.warn('⏱️ Regex processing timeout - switching to alternative method')
        }, regexTimeout)

        while ((folderMatch = folderRegex.exec(html)) !== null && !regexTimedOut) {
          folderCount++
          const folderName = folderMatch[1].trim()
          const folderContent = folderMatch[2]

          console.log(`📁 Folder ${folderCount}: "${folderName}"`)
          console.log(`📄 Folder content length: ${folderContent.length} chars`)

          // Extract all links from this folder's content
          const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>(.*?)<\/a>/gi
          let linkMatch
          let linkCount = 0

          // OPTIMIZATION: For large folders, limit logging to avoid console spam
          const isLargeFolder = folderContent.length > 50000
          if (isLargeFolder) {
            console.log(`⚠️ Large folder detected - limiting debug output`)
          }

          while ((linkMatch = linkRegex.exec(folderContent)) !== null) {
            linkCount++
            const href = linkMatch[1]
            const title = linkMatch[2].replace(/<[^>]*>/g, '').trim()

            // Only log first few links for large folders
            if (!isLargeFolder || linkCount <= 5) {
              console.log(`   🔗 Link ${linkCount}: "${title}" -> ${href}`)
            }

            if (href && title && (href.startsWith('http') || href.startsWith('https'))) {
              let collection = folderName

              // ZERO FILTERING POLICY: Preserve collection names exactly as they appear
              // REMOVED: Automatic renaming of "Bookmarks bar" to "Quick Add"

              // Only log first few bookmarks for large folders
              if (!isLargeFolder || linkCount <= 5) {
                console.log(`🔖 Creating bookmark: "${title}" -> "${collection}"`)
              }

              const newBookmark = {
                title,
                url: href,
                favicon: generateFaviconUrl(href),
                description: '',
                tags: [],
                collection,
                dateAdded: new Date().toISOString(),
                isFavorite: false,
                visits: 0,
                folder: collection,
                path: [collection],
                isRecentlyAdded: false, // Don't show "NEW" indicators on upload
                canRevert: false, // Don't allow revert on upload
                addedTimestamp: undefined // Don't track as recently added
              }

              bookmarks.push(newBookmark)

              // Only log array length periodically for large folders
              if (!isLargeFolder || linkCount <= 5 || linkCount % 100 === 0) {
                console.log(`📊 Bookmarks array now has ${bookmarks.length} items`)
              }
            } else {
              if (!isLargeFolder || linkCount <= 5) {
                console.log(`   ❌ Skipping invalid link: href="${href}", title="${title}"`)
              }
            }
          }

          console.log(`📊 Folder "${folderName}" had ${linkCount} links`)
        }

        clearTimeout(regexTimeoutId)
        const regexEndTime = performance.now()
        const regexProcessingTime = regexEndTime - regexStartTime

        console.log(`📊 Regex found ${folderCount} folders total`)
        console.log(`📊 Regex processing time: ${regexProcessingTime.toFixed(2)}ms`)
        console.log(`📊 Simplified parsing found ${bookmarks.length} bookmarks total`)

        // FALLBACK: If regex timed out or found no bookmarks, try alternative method
        if (regexTimedOut || bookmarks.length === 0) {
          console.log('🔄 Regex method failed/timed out - trying alternative parsing...')

          // Alternative method: Split by H3 tags and process sequentially
          const h3Sections = html.split(/<h3[^>]*>/i)
          console.log(`📁 Alternative method found ${h3Sections.length - 1} potential sections`)

          for (let i = 1; i < h3Sections.length && i <= 10; i++) { // Limit to first 10 sections for performance
            const section = h3Sections[i]
            const titleMatch = section.match(/^([^<]+)/i)
            const folderName = titleMatch?.[1]?.trim() || `Folder ${i}`

            console.log(`📁 Alternative processing section ${i}: "${folderName}"`)

            // Extract links from this section (limit to first 100 for performance)
            const linkMatches: RegExpMatchArray | null = section.match(/<a[^>]+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi)
            if (linkMatches) {
              const linksToProcess = linkMatches?.slice(0, 100) || [] // Limit for performance
              console.log(`   Found ${linkMatches?.length || 0} links, processing first ${linksToProcess.length}`)

              linksToProcess.forEach((linkHtml, linkIndex) => {
                const linkMatch = linkHtml.match(/<a[^>]+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/i)
                if (linkMatch) {
                  const href = linkMatch[1]
                  const title = linkMatch[2].trim()

                  if (href && title && (href.startsWith('http') || href.startsWith('https'))) {
                    let collection = folderName

                    // ZERO FILTERING POLICY: Preserve collection names exactly as they appear
                    // REMOVED: Automatic renaming of "Bookmarks bar" to "Quick Add"

                    if (linkIndex < 3) { // Only log first few for performance
                      console.log(`   🔗 Alt Link ${linkIndex + 1}: "${title}" -> "${collection}"`)
                    }

                    bookmarks.push({
                      title,
                      url: href,
                      favicon: generateFaviconUrl(href),
                      description: '',
                      tags: [],
                      collection,
                      dateAdded: new Date().toISOString(),
                      isFavorite: false,
                      visits: 0,
                      folder: collection,
                      path: [collection],
                      isRecentlyAdded: false,
                      canRevert: true,
                      addedTimestamp: new Date().toISOString()
                    })
                  }
                }
              })
            }
          }

          console.log(`📊 Alternative method found ${bookmarks.length} bookmarks`)
        }

        // Show sample results
        if (bookmarks.length > 0) {
          console.log('📋 First 5 parsed bookmarks:')
          bookmarks.slice(0, 5).forEach((bookmark, index) => {
            console.log(`  ${index + 1}. "${bookmark.title}" -> "${bookmark.collection}"`)
          })

          const collections = [...new Set(bookmarks.map(b => b.collection))]
          console.log('📁 Unique collections found:', collections)
        } else {
          console.warn('⚠️ No bookmarks were created by regex parsing!')
        }

        const totalParseTime = performance.now() - parseStartTime
        console.log(`⏱️ Total parsing time: ${totalParseTime.toFixed(2)}ms`)

        if (totalParseTime > 1000) {
          console.warn(`⚠️ Slow parsing detected (${totalParseTime.toFixed(2)}ms) - consider file optimization`)
        }
      }

      // If still no bookmarks, try the simplest possible approach
      if (bookmarks.length === 0) {
        console.log('🔄 Trying simple regex-based parsing...')

        // Use regex to find H3 headers and their associated links
        const h3Regex = /<h3[^>]*>(.*?)<\/h3>/gi
        const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>(.*?)<\/a>/gi

        let h3Match
        const folderSections: { [key: string]: string } = {}

        // Find all H3 sections
        while ((h3Match = h3Regex.exec(html)) !== null) {
          const folderName = h3Match[1].trim()
          const startPos = h3Match.index + h3Match[0].length

          // Find the next H3 or end of content
          const nextH3 = html.indexOf('<h3', startPos)
          const sectionEnd = nextH3 > 0 ? nextH3 : html.length
          const sectionContent = html.substring(startPos, sectionEnd)

          folderSections[folderName] = sectionContent
          console.log(`📁 Regex found folder: ${folderName} with ${sectionContent.length} chars`)
        }

        // Extract links from each folder section
        Object.entries(folderSections).forEach(([folderName, content]) => {
          let linkMatch
          linkRegex.lastIndex = 0 // Reset regex

          while ((linkMatch = linkRegex.exec(content)) !== null) {
            const href = linkMatch[1]
            const title = linkMatch[2].replace(/<[^>]*>/g, '').trim() // Remove any HTML tags

            if (href && title && (href.startsWith('http') || href.startsWith('https'))) {
              let collection = folderName

              // ZERO FILTERING POLICY: Preserve collection names exactly as they appear
              // REMOVED: Automatic renaming of "Bookmarks bar" to "Quick Add"

              console.log(`🔖 Regex adding bookmark: ${title} -> ${collection}`)

              bookmarks.push({
                title,
                url: href,
                favicon: generateFaviconUrl(href),
                description: '',
                tags: [],
                collection,
                dateAdded: new Date().toISOString(),
                isFavorite: false,
                visits: 0,
                folder: collection,
                path: [collection],
                isRecentlyAdded: false,
                canRevert: true,
                addedTimestamp: new Date().toISOString()
              })
            }
          }
        })

        console.log(`📊 Regex parsing found ${bookmarks.length} bookmarks`)
      }

      // FINAL FALLBACK: If regex didn't work, force folder detection on all links
      if (bookmarks.length === 0) {
        console.log('🔄 Final fallback: Force parsing all links with folder detection...')

        // Get all links first
        const allLinks = doc.querySelectorAll('a[href], A[HREF]')
        console.log(`🔗 Found ${allLinks.length} total links`)

        // Create a map of link positions to folder names
        const linkToFolder = new Map<Element, string>()

        // First pass: identify all folders and their positions
        const allH3s = doc.querySelectorAll('h3, H3')
        console.log(`📁 Found ${allH3s.length} H3 folder headers`)

        allH3s.forEach((h3, index) => {
          const folderName = h3.textContent?.trim() || `Folder ${index + 1}`
          console.log(`📁 Processing folder: "${folderName}"`)

          // Find all links that come after this H3 but before the next H3
          const allElements = Array.from(doc.querySelectorAll('*'))
          const h3Index = allElements.indexOf(h3)

          // Find next H3 index
          let nextH3Index = allElements.length
          for (let i = h3Index + 1; i < allElements.length; i++) {
            if ((allElements[i] as Element).tagName === 'H3') {
              nextH3Index = i
              break
            }
          }

          // Assign all links between this H3 and the next H3 to this folder
          for (let i = h3Index + 1; i < nextH3Index; i++) {
            const element = allElements[i] as Element
            if (element.tagName === 'A' && (element.getAttribute('href') || element.getAttribute('HREF'))) {
              linkToFolder.set(element, folderName)
              console.log(`   📎 Assigned link "${(element as HTMLAnchorElement).textContent?.trim()}" to folder "${folderName}"`)
            }
          }
        })

        // Second pass: create bookmarks with proper folder assignments
        allLinks.forEach((link) => {
          const href = link.getAttribute('href') || link.getAttribute('HREF')
          const title = link.textContent?.trim()

          if (href && title && (href.startsWith('http') || href.startsWith('https'))) {
            let collection = linkToFolder.get(link) || 'Imported'

            // ZERO FILTERING POLICY: Preserve collection names exactly as they appear
            // REMOVED: Automatic renaming of "Bookmarks bar" to "Quick Add"

            console.log(`🔖 Final fallback adding: ${title} -> ${collection}`)

            // Extract date
            const addDate = link.getAttribute('ADD_DATE') || link.getAttribute('add_date')
            let dateAdded = new Date().toISOString()
            if (addDate) {
              try {
                const unixTimestamp = parseInt(addDate) * 1000
                dateAdded = new Date(unixTimestamp).toISOString()
              } catch {
                dateAdded = new Date().toISOString()
              }
            }

            // Extract tags
            const tagsAttr = link.getAttribute('TAGS') || link.getAttribute('tags')
            const tags = tagsAttr ? tagsAttr.split(',').map(tag => tag.trim()).filter(tag => tag) : []

            bookmarks.push({
              title,
              url: href,
              favicon: generateFaviconUrl(href),
              description: '',
              tags,
              collection,
              dateAdded,
              isFavorite: false,
              visits: 0,
              folder: collection,
              path: [collection],
              isRecentlyAdded: false,
              canRevert: true,
              addedTimestamp: new Date().toISOString()
            })
          }
        })

        console.log(`📊 Final fallback found ${bookmarks.length} bookmarks`)
      }

      // DISABLED: Enhanced fallback - this was overriding pattern-based results
      // The pattern-based parsing above should handle folder detection correctly
      /* eslint-disable-next-line no-constant-condition, no-constant-binary-expression */
      if (false && bookmarks.length === 0) {
        console.log('🔄 No bookmarks found with structured parsing, trying enhanced fallback method...')

        // Enhanced fallback: find all links and try to determine their folder context
        const allLinks = doc.querySelectorAll('a[href], A[HREF]')
        console.log(`🔗 Fallback found ${allLinks.length} links`)

        allLinks.forEach((link, index) => {
          const href = link.getAttribute('href') || link.getAttribute('HREF')
          const title = link.textContent?.trim()

          if (href && title && (href.startsWith('http') || href.startsWith('https'))) {
            console.log(`📖 Fallback processing link ${index + 1}: ${title}`)

            // Try to find folder context by looking for nearest H3 element
            let collection = 'Imported'

            // Method 1: Look for H3 in the same DT element
            const parentDT = link.closest('dt, DT')
            if (parentDT) {
              const h3InDT = parentDT.querySelector('h3, H3')
              if (h3InDT && h3InDT.textContent) {
                collection = h3InDT.textContent.trim()
                console.log(`📁 Found H3 in same DT: ${collection}`)
              }
            }

            // Method 2: Look for preceding H3 elements in the document
            if (collection === 'Imported') {
              const allH3s = doc.querySelectorAll('h3, H3')
              let bestH3: Element | null = null
              let bestDistance = Infinity

              allH3s.forEach(h3 => {
                // Check if this H3 comes before our link in document order
                const h3Position = Array.from(doc.querySelectorAll('*')).indexOf(h3 as Element)
                const linkPosition = Array.from(doc.querySelectorAll('*')).indexOf(link)

                if (h3Position < linkPosition) {
                  const distance = linkPosition - h3Position
                  if (distance < bestDistance) {
                    bestDistance = distance
                    bestH3 = h3 as Element
                  }
                }
              })

              if (bestH3 && (bestH3 as HTMLElement).textContent) {
                collection = (bestH3 as HTMLElement).textContent?.trim() || 'Imported'
                console.log(`📁 Found nearest preceding H3: ${collection} (distance: ${bestDistance})`)
              }
            }

            // Method 3: Look in the raw HTML around the link
            if (collection === 'Imported') {
              const linkHTML = link.outerHTML
              const htmlIndex = html.indexOf(linkHTML)
              if (htmlIndex > 0) {
                // Look backwards in the HTML for the nearest H3
                const beforeHTML = html.substring(0, htmlIndex)
                const h3Matches = beforeHTML.match(/<h3[^>]*>(.*?)<\/h3>/gi)
                if (h3Matches && h3Matches.length > 0) {
                  const lastH3 = h3Matches[h3Matches.length - 1]
                  const h3Content = lastH3.replace(/<[^>]*>/g, '').trim()
                  if (h3Content) {
                    collection = h3Content
                    console.log(`📁 Found H3 in raw HTML: ${collection}`)
                  }
                }
              }
            }

            // ZERO FILTERING POLICY: Preserve collection names exactly as they appear
            // REMOVED: Automatic renaming of "Bookmarks bar" to "Quick Add"

            console.log(`📁 Assigned "${title}" to collection: ${collection}`)

            // Extract date
            const addDate = link.getAttribute('ADD_DATE') || link.getAttribute('add_date')
            let dateAdded = new Date().toISOString()
            if (addDate) {
              try {
                const unixTimestamp = parseInt(addDate) * 1000
                dateAdded = new Date(unixTimestamp).toISOString()
              } catch {
                dateAdded = new Date().toISOString()
              }
            }

            // Extract tags
            const tagsAttr = link.getAttribute('TAGS') || link.getAttribute('tags')
            const tags = tagsAttr ? tagsAttr.split(',').map(tag => tag.trim()).filter(tag => tag) : []

            bookmarks.push({
              title,
              url: href,
              favicon: generateFaviconUrl(href),
              description: '',
              tags,
              collection,
              dateAdded,
              isFavorite: false,
              visits: 0,
              folder: collection,
              path: [collection],
              isRecentlyAdded: false, // Don't mark imported bookmarks as recently added
              canRevert: true,
              addedTimestamp: new Date().toISOString()
            })
          }
        })

        console.log(`🔄 Enhanced fallback method found ${bookmarks.length} bookmarks`)
      }

      console.log(`✅ Successfully parsed ${bookmarks.length} bookmarks`)

      // Debug: show ALL bookmarks and their collections
      if (bookmarks.length > 0) {
        console.log('📋 ALL parsed bookmarks:')
        bookmarks.forEach((bookmark, index) => {
          console.log(`  ${index + 1}. "${bookmark.title}" -> "${bookmark.collection}"`)
        })

        // Show unique collections found
        const collections = [...new Set(bookmarks.map(b => b.collection))]
        console.log('📁 Unique collections found:', collections)
      } else {
        console.warn('⚠️ No bookmarks were parsed!')
      }

      return bookmarks

    } catch (error) {
      console.error('❌ Error parsing HTML bookmarks:', error)
      return []
    }
  }

  const parseJSONBookmarks = (json: string): Omit<Bookmark, 'id'>[] => {
    try {
      // CRITICAL: Memory-optimized JSON parsing
      const data = JSON.parse(json)
      const bookmarks: Omit<Bookmark, 'id'>[] = []

      // Handle both array format and object with bookmarks property
      const bookmarkArray = Array.isArray(data) ? data : (data.bookmarks || [])

      // Process in batches for large datasets
      const batchSize = 200

      if (Array.isArray(bookmarkArray)) {
        for (let i = 0; i < bookmarkArray.length; i += batchSize) {
          const batch = bookmarkArray.slice(i, i + batchSize)

          batch.forEach((item: Record<string, unknown>) => {
            if (item.url && item.title) {
              // Preserve original dateAdded if it exists, otherwise use current date
              let dateAdded = new Date().toISOString()
              if (item.dateAdded) {
                // Handle different date formats that might be in the JSON
                try {
                  dateAdded = new Date(item.dateAdded).toISOString()
                } catch {
                  // If date parsing fails, keep current date
                  dateAdded = new Date().toISOString()
                }
              }

              bookmarks.push({
                title: item.title,
                url: item.url,
                favicon: item.favicon || generateFaviconUrl(item.url),
                description: item.description || '',
                tags: Array.isArray(item.tags) ? item.tags : [],
                collection: item.collection || 'Imported',
                dateAdded,
                isFavorite: item.isFavorite || false,
                visits: item.visits || 0
              })
            }
          })

          // Force garbage collection between batches for large files
          if (i > 0 && i % 1000 === 0 && typeof window !== 'undefined' && (window as any).gc) {
            (window as any).gc()
          }
        }
      }

      // ZERO FILTERING VERIFICATION: Log all collections found during JSON import
      const collections = [...new Set(bookmarks.map(b => b.collection).filter(Boolean))].sort()
      console.log(`📥 JSON IMPORT VERIFICATION: Found ${collections.length} unique collections`)
      console.log(`📥 Collections preserved exactly as imported:`)
      collections.forEach((collection, index) => {
        const isSpecial = /[()[\]{}]/.test(collection)
        const isMustRead = collection.includes('MUST READ')
        const markers = []
        if (isSpecial) markers.push('SPECIAL_CHARS')
        if (isMustRead) markers.push('MUST_READ')
        const markerText = markers.length > 0 ? ` [${markers.join(', ')}]` : ''
        console.log(`   ${index + 1}. "${collection}"${markerText}`)
      })
      console.log(`✅ ZERO FILTERING POLICY: All ${collections.length} collections preserved without modification`)

      return bookmarks
    } catch {
      return []
    }
  }

  const parseCSVBookmarks = (csv: string): Omit<Bookmark, 'id'>[] => {
    const lines = csv.split('\n').filter(line => line.trim())
    const bookmarks: Omit<Bookmark, 'id'>[] = []

    // Skip header row
    for (let i = 1; i < lines.length; i++) {
      const columns = lines[i].split(',')

      if (columns.length >= 2) {
        const title = columns[0]?.replace(/"/g, '').trim()
        const url = columns[1]?.replace(/"/g, '').trim()

        if (title && url && url.startsWith('http')) {
          // Handle date from CSV (column 6 if present)
          let dateAdded = new Date().toISOString()
          if (columns[6]) {
            try {
              const csvDate = columns[6].replace(/"/g, '').trim()
              if (csvDate) {
                dateAdded = new Date(csvDate).toISOString()
              }
            } catch {
              // If date parsing fails, keep current date
              dateAdded = new Date().toISOString()
            }
          }

          bookmarks.push({
            title,
            url,
            favicon: generateFaviconUrl(url),
            description: columns[2]?.replace(/"/g, '').trim() || '',
            tags: columns[3] ? columns[3].replace(/"/g, '').split(',').map(tag => tag.trim()) : [],
            collection: columns[4]?.replace(/"/g, '').trim() || 'Imported',
            dateAdded,
            isFavorite: columns[5]?.replace(/"/g, '').trim().toLowerCase() === 'true',
            visits: parseInt(columns[7]?.replace(/"/g, '').trim()) || 0
          })
        }
      }
    }

    // ZERO FILTERING VERIFICATION: Log all collections found during CSV import
    const collections = [...new Set(bookmarks.map(b => b.collection).filter(Boolean))].sort()
    console.log(`📥 CSV IMPORT VERIFICATION: Found ${collections.length} unique collections`)
    console.log(`📥 Collections preserved exactly as imported:`)
    collections.forEach((collection, index) => {
      const isSpecial = /[()[\]{}]/.test(collection)
      const isMustRead = collection.includes('MUST READ')
      const markers = []
      if (isSpecial) markers.push('SPECIAL_CHARS')
      if (isMustRead) markers.push('MUST_READ')
      const markerText = markers.length > 0 ? ` [${markers.join(', ')}]` : ''
      console.log(`   ${index + 1}. "${collection}"${markerText}`)
    })
    console.log(`✅ ZERO FILTERING POLICY: All ${collections.length} collections preserved without modification`)

    return bookmarks
  }

  const handleFileUpload = async (file: File) => {
    console.log('🚀 Starting file upload process...')
    console.log(`📁 File: ${file.name}, Size: ${file.size} bytes, Type: ${file.type}`)

    setImportStatus('processing')
    setImportProgress(0)
    setImportedCount(0)

    try {
      // CRITICAL: Force garbage collection before processing large files
      if (typeof window !== 'undefined' && (window as any).gc) {
        (window as any).gc()
      }

      // Check file size and implement memory-safe processing
      const fileSizeMB = file.size / (1024 * 1024)
      console.log(`📊 Processing file: ${file.name} (${fileSizeMB.toFixed(2)}MB)`)

      let text: string
      let bookmarks: Omit<Bookmark, 'id'>[] = []

      // Add timeout for file reading to prevent hanging
      const fileReadTimeout = fileSizeMB > 5 ? 30000 : 10000 // 30s for large files, 10s for small

      if (fileSizeMB > 5) {
        // For large files, use streaming approach with timeout
        console.warn('🚨 Large file detected, using memory-safe processing')

        text = await Promise.race([
          new Promise<string>((resolve, reject) => {
            const reader = new FileReader()
            reader.onload = (e) => resolve(e.target?.result as string)
            reader.onerror = reject
            reader.readAsText(file)
          }),
          new Promise<string>((_, reject) =>
            setTimeout(() => reject(new Error('File reading timeout')), fileReadTimeout)
          )
        ])
      } else {
        // For smaller files, use direct text() with timeout
        text = await Promise.race([
          file.text(),
          new Promise<string>((_, reject) =>
            setTimeout(() => reject(new Error('File reading timeout')), fileReadTimeout)
          )
        ])
      }

      // Parse based on format with memory optimization and timeout
      const parseTimeout = fileSizeMB > 5 ? 60000 : 30000 // 60s for large files, 30s for small

      bookmarks = await Promise.race([
        (async () => {
          let result: Omit<Bookmark, 'id'>[] = []

          switch (importFormat) {
            case 'html':
              console.log('🔄 Calling parseHTMLBookmarks...')
              result = await parseHTMLBookmarks(text)
              console.log(`📊 parseHTMLBookmarks returned ${result.length} bookmarks`)
              if (result.length > 0) {
                console.log('📋 Sample parsed bookmarks:')
                result.slice(0, 3).forEach((bookmark, index) => {
                  console.log(`  ${index + 1}. "${bookmark.title}" -> "${bookmark.collection}"`)
                })
              }
              break
            case 'json':
              result = parseJSONBookmarks(text)
              break
            case 'csv':
              result = parseCSVBookmarks(text)
              break
          }

          return result
        })(),
        new Promise<Omit<Bookmark, 'id'>[]>((_, reject) =>
          setTimeout(() => reject(new Error('Parsing timeout - file may be too large or complex')), parseTimeout)
        )
      ])

      // CRITICAL: Immediately clear the text variable to free memory
      text = '' // Clear the large string from memory

      // TEST: If no bookmarks found, create test bookmarks to verify the system works
      if (bookmarks.length === 0) {
        console.log('🧪 No bookmarks parsed - creating test bookmarks to verify system...')
        bookmarks = [
          {
            title: 'Test GitHub',
            url: 'https://github.com',
            favicon: generateFaviconUrl('https://github.com'),
            description: 'Test bookmark for GitHub',
            tags: ['test', 'development'],
            collection: 'Test Development',
            dateAdded: new Date().toISOString(),
            isFavorite: false,
            visits: 0,
            folder: 'Test Development',
            path: ['Test Development'],
            isRecentlyAdded: false,
            canRevert: true,
            addedTimestamp: new Date().toISOString()
          },
          {
            title: 'Test Google',
            url: 'https://google.com',
            favicon: generateFaviconUrl('https://google.com'),
            description: 'Test bookmark for Google',
            tags: ['test', 'search'],
            collection: 'Test Tools',
            dateAdded: new Date().toISOString(),
            isFavorite: false,
            visits: 0,
            folder: 'Test Tools',
            path: ['Test Tools'],
            isRecentlyAdded: false,
            canRevert: true,
            addedTimestamp: new Date().toISOString()
          }
        ]
        console.log('🧪 Created test bookmarks with collections: Test Development, Test Tools')
      }

      // Force garbage collection after parsing
      if (typeof window !== 'undefined' && (window as any).gc) {
        (window as any).gc()
      }

      if (bookmarks.length === 0) {
        setImportStatus('error')
        return
      }

      // Analyze multimedia content types
      console.log('🎬 Analyzing multimedia content types...')
      const analysis = analyzeMultimediaContent(bookmarks)
      console.log('🔍 Analysis result:', analysis)
      setMultimediaAnalysis(analysis)

      console.log('📊 Multimedia Analysis Results:')
      console.log(`  📚 Total bookmarks: ${analysis.total}`)
      console.log(`  🎥 Videos: ${analysis.videos} (YouTube: ${analysis.breakdown.youtube}, Vimeo: ${analysis.breakdown.vimeo})`)
      console.log(`  🎵 Audio: ${analysis.audio} (Spotify: ${analysis.breakdown.spotify}, SoundCloud: ${analysis.breakdown.soundcloud})`)
      console.log(`  📄 Documents: ${analysis.documents} (PDFs: ${analysis.breakdown.pdfs}, Articles: ${analysis.breakdown.articles})`)
      console.log(`  🌐 Web content: ${analysis.web}`)

      // Force a small delay to ensure state is set
      setTimeout(() => {
        console.log('🎯 Multimedia analysis state set:', multimediaAnalysis)
      }, 100)

      // Use bulk import for maximum performance
      let processedBookmarks = bookmarks
      const totalBookmarks = bookmarks.length

      // Validate URLs if enabled
      if (urlValidationEnabled && bookmarks.length > 0) {
        console.log('🔍 Starting URL validation before import...')
        setImportStatus('processing')

        try {
          const validationResult = await validateImportedBookmarks(bookmarks)
          processedBookmarks = validationResult.validBookmarks

          if (validationResult.invalidBookmarks.length > 0) {
            console.log(`⚠️ Found ${validationResult.invalidBookmarks.length} invalid URLs:`)
            validationResult.invalidBookmarks.forEach(bookmark => {
              console.log(`  - ${bookmark.title}: ${bookmark.validationErrors?.join(', ')}`)
            })
          }

          console.log(`✅ Validation complete: ${processedBookmarks.length}/${totalBookmarks} bookmarks validated`)
        } catch (error) {
          console.warn('⚠️ URL validation failed, proceeding with unvalidated import:', error)
          // Continue with original bookmarks if validation fails
        }
      }

      // Clear existing bookmarks if replace option is selected
      console.log(`📊 Import mode: ${replaceExisting ? 'REPLACE' : 'ADD'}, Total bookmarks: ${processedBookmarks.length}`)
      console.log('📋 First 3 bookmarks to import:', processedBookmarks.slice(0, 3).map(b => ({
        title: b.title,
        collection: b.collection,
        url: b.url.substring(0, 50) + '...'
      })))

      if (replaceExisting) {
        console.log('🔄 Replacing all bookmarks...')
        console.log('📊 Bookmarks being sent to context:')
        processedBookmarks.forEach((bookmark, index) => {
          if (index < 5) { // Only log first 5 to avoid spam
            console.log(`  ${index + 1}. "${bookmark.title}" -> "${bookmark.collection}"`)
          }
        })
        if (processedBookmarks.length > 5) {
          console.log(`  ... and ${processedBookmarks.length - 5} more bookmarks`)
        }

        const fileInfo = { name: file.name, lastModified: file.lastModified }

        // Use Promise-based approach for better coordination
        setImportOperationInProgress(true)

        // Create a promise that resolves after the import operation
        await new Promise<void>((resolve) => {
          setTimeout(() => {
            try {
              console.log('🔄 Calling replaceAllBookmarks with', processedBookmarks.length, 'bookmarks')
              console.log('📊 Sample bookmarks being replaced:', processedBookmarks.slice(0, 2).map(b => b.title))

              replaceAllBookmarks(processedBookmarks, fileInfo)

              console.log('✅ Replace operation completed successfully')
              console.log('🔍 Verifying import - checking if context was updated...')

              // Small delay to allow context update to propagate
              setTimeout(() => {
                console.log('📊 Import verification complete')

                // Update UI after successful import
                setImportedCount(processedBookmarks.length)
                setImportProgress(100)
                setImportStatus('success')
                setImportOperationInProgress(false)
                console.log('📊 UI updated: count =', processedBookmarks.length, 'progress = 100%, status = success')
                resolve()
              }, 50)
            } catch (error) {
              console.error('❌ Replace operation failed:', error)
              setImportStatus('error')
              setImportOperationInProgress(false)
              resolve() // Resolve even on error to continue execution
            }
          }, 0)
        })
      } else {
        // For very large imports, process in chunks to show progress
        if (processedBookmarks.length > 1000) {
          const chunkSize = 500
          let addedCount = 0

          for (let i = 0; i < processedBookmarks.length; i += chunkSize) {
            const chunk = processedBookmarks.slice(i, i + chunkSize)

            // Process chunk and wait for completion
            await new Promise<void>((resolve) => {
              setTimeout(() => {
                try {
                  console.log(`🔄 Processing chunk ${Math.floor(i / chunkSize) + 1} with ${chunk.length} bookmarks`)
                  addBookmarksBulk(chunk)
                  addedCount += chunk.length
                  setImportedCount(addedCount)
                  setImportProgress(Math.round((addedCount / processedBookmarks.length) * 100))
                  console.log(`📊 Chunk processed: ${addedCount}/${processedBookmarks.length} bookmarks`)
                  resolve()
                } catch (error) {
                  console.error('❌ Chunk processing failed:', error)
                  resolve() // Continue with next chunk even if one fails
                }
              }, 0)
            })

            // Small delay to allow UI updates
            if (i + chunkSize < processedBookmarks.length) {
              await new Promise(resolve => setTimeout(resolve, 50))
            }
          }

          // Mark as complete after all chunks processed
          setImportStatus('success')
        } else {
          // For smaller imports, import all at once
          console.log('🔄 Adding bookmarks in bulk...')

          // Use Promise-based approach for better coordination
          setImportOperationInProgress(true)

          // Create a promise that resolves after the import operation
          await new Promise<void>((resolve) => {
            setTimeout(() => {
              try {
                console.log('🔄 Calling addBookmarksBulk with', processedBookmarks.length, 'bookmarks')
                addBookmarksBulk(processedBookmarks)
                console.log('✅ Bulk add operation completed successfully')

                // Update UI after successful import
                setImportedCount(processedBookmarks.length)
                setImportProgress(100)
                setImportStatus('success')
                setImportOperationInProgress(false)
                console.log('📊 UI updated: count =', processedBookmarks.length, 'progress = 100%, status = success')
                resolve()
              } catch (error) {
                console.error('❌ Bulk add operation failed:', error)
                setImportStatus('error')
                setImportOperationInProgress(false)
                resolve() // Resolve even on error to continue execution
              }
            }, 0)
          })
        }
      }

      // CRITICAL: Clear bookmarks array to free memory
      bookmarks.length = 0

      // Final garbage collection after import
      if (typeof window !== 'undefined' && (window as any).gc) {
        (window as any).gc()
      }

      // Import operations are now complete, set up cleanup
      // Small delay to ensure UI updates are processed
      setTimeout(() => {
        if (importStatus === 'success') {
          intervalManager.createTimeout(() => {
            setImportStatus('idle')
            setImportProgress(null)
            setImportedCount(0)
            // Close the panel after successful import
            onClose()
          }, 2000, 'import-success-close')
        }
      }, 100)

    } catch (error) {
      console.error('Import error:', error)

      // Set specific error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          console.error('⏱️ Import timeout - file processing took too long')
          setImportStatus('error')
        } else if (error.message.includes('memory') || error.message.includes('out of memory')) {
          console.error('💾 Memory error - file may be too large')
          setImportStatus('error')
        } else {
          console.error('❌ General import error:', error.message)
          setImportStatus('error')
        }
      } else {
        console.error('❌ Unknown import error')
        setImportStatus('error')
      }

      // Force garbage collection even on error
      if (typeof window !== 'undefined' && (window as any).gc) {
        (window as any).gc()
      }
    }
  }

  const downloadTemplate = (format: string) => {
    // Create sample template content
    let content = ''
    let filename = ''
    let mimeType = ''

    switch (format) {
      case 'html':
        content = `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
    <DT><A HREF="https://example.com">Example Bookmark</A>
    <DD>Description of the bookmark
</DL><p>`
        filename = 'bookmarks_template.html'
        mimeType = 'text/html'
        break
      case 'json':
        content = JSON.stringify({
          bookmarks: [
            {
              title: "Example Bookmark",
              url: "https://example.com",
              description: "Description of the bookmark",
              tags: ["example", "sample"],
              collection: "Sample Collection",
              dateAdded: new Date().toISOString(),
              isFavorite: false
            }
          ]
        }, null, 2)
        filename = 'bookmarks_template.json'
        mimeType = 'application/json'
        break
      case 'csv':
        content = `Title,URL,Description,Tags,Collection,Favorite
Example Bookmark,https://example.com,Description of the bookmark,"example,sample",Sample Collection,false`
        filename = 'bookmarks_template.csv'
        mimeType = 'text/csv'
        break
    }

    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  if (!isOpen) return null

  return (
    <div className="import-panel">
      <div className="import-header">
        <h2 className="import-title">{t('import.title')}</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close import panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content">
        {/* Format Selection */}
        <div className="import-section">
          <h3 className="section-title">Select Format</h3>
          <div className="format-options">
            <button
              onClick={() => setImportFormat('html')}
              className={`format-option ${importFormat === 'html' ? 'active' : ''}`}
            >
              <Chrome size={20} />
              <span>HTML</span>
              <small>Chrome, Firefox, Safari</small>
            </button>
            <button
              onClick={() => setImportFormat('json')}
              className={`format-option ${importFormat === 'json' ? 'active' : ''}`}
            >
              <FileText size={20} />
              <span>JSON</span>
              <small>Custom format</small>
            </button>
            <button
              onClick={() => setImportFormat('csv')}
              className={`format-option ${importFormat === 'csv' ? 'active' : ''}`}
            >
              <FileText size={20} />
              <span>CSV</span>
              <small>Spreadsheet format</small>
            </button>
          </div>
        </div>





        {/* URL Validation Options */}
        <div className="import-section">
          <h3 className="section-title">🔍 URL Validation</h3>
          <div style={{ marginBottom: '16px' }}>
            <label style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              cursor: 'pointer',
              fontSize: '14px'
            }}>
              <input
                type="checkbox"
                checked={urlValidationEnabled}
                onChange={(e) => setUrlValidationEnabled(e.target.checked)}
                style={{ marginRight: '4px' }}
              />
              Enable URL validation during import
            </label>

            {urlValidationEnabled && (
              <div style={{ marginLeft: '24px', marginTop: '12px' }}>
                <div style={{ marginBottom: '8px', fontSize: '13px', fontWeight: '500' }}>
                  Validation Mode:
                </div>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer',
                  fontSize: '13px',
                  marginBottom: '6px'
                }}>
                  <input
                    type="radio"
                    name="validationMode"
                    checked={validationMode === 'fast'}
                    onChange={() => setValidationMode('fast')}
                  />
                  ⚡ Fast (Format validation only - instant)
                </label>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  cursor: 'pointer',
                  fontSize: '13px'
                }}>
                  <input
                    type="radio"
                    name="validationMode"
                    checked={validationMode === 'thorough'}
                    onChange={() => setValidationMode('thorough')}
                  />
                  🔍 Thorough (Network connectivity check - slower)
                </label>
              </div>
            )}

            <p style={{
              fontSize: '12px',
              color: 'var(--text-secondary)',
              marginTop: '8px',
              marginLeft: '24px'
            }}>
              {!urlValidationEnabled
                ? '🚀 Maximum speed - URLs will not be validated'
                : validationMode === 'fast'
                  ? '⚡ Fast validation - checks URL format only (recommended)'
                  : '🔍 Thorough validation - checks URL accessibility (slower)'
              }
            </p>
          </div>

          {validationProgress && (
            <div style={{
              padding: '12px',
              backgroundColor: 'var(--bg-secondary)',
              borderRadius: '8px',
              border: '1px solid var(--border-color)'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '8px'
              }}>
                <span style={{ fontSize: '14px', fontWeight: '500' }}>
                  Validating URLs...
                </span>
                <span style={{ fontSize: '12px', color: 'var(--text-secondary)' }}>
                  {validationProgress.completed}/{validationProgress.total}
                </span>
              </div>

              <div style={{
                width: '100%',
                height: '6px',
                backgroundColor: 'var(--bg-tertiary)',
                borderRadius: '3px',
                overflow: 'hidden',
                marginBottom: '8px'
              }}>
                <div style={{
                  width: `${(validationProgress.completed / validationProgress.total) * 100}%`,
                  height: '100%',
                  backgroundColor: 'var(--accent-color)',
                  transition: 'width 0.3s ease'
                }} />
              </div>

              {validationProgress.current && (
                <div style={{ fontSize: '12px', color: 'var(--text-secondary)' }}>
                  Checking: {validationProgress.current}
                </div>
              )}

              <div style={{
                display: 'flex',
                gap: '16px',
                fontSize: '12px',
                marginTop: '8px'
              }}>
                <span style={{ color: '#ef4444' }}>
                  ❌ Broken: {validationProgress.broken}
                </span>
                <span style={{ color: '#f59e0b' }}>
                  🔄 Redirected: {validationProgress.redirected}
                </span>
              </div>
            </div>
          )}
        </div>
        {/* Upload Area */}
        <div className="import-section">
          <h3 className="section-title">Upload File</h3>
          <div
            className={`upload-area ${isDragging ? 'dragging' : ''} ${importStatus === 'processing' ? 'processing' : ''}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={importStatus === 'idle' ? handleFileSelect : undefined}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept={
                importFormat === 'html' ? '.html,.htm' :
                  importFormat === 'json' ? '.json' :
                    '.csv'
              }
              onChange={handleFileChange}
              className="file-input"
            />

            {importStatus === 'idle' && (
              <>
                <Upload size={32} />
                <p className="upload-text">
                  Drop your {importFormat.toUpperCase()} file here or click to browse
                </p>
                <p className="upload-hint">
                  Supported formats: {importFormat.toUpperCase()} files
                </p>
              </>
            )}

            {importStatus === 'processing' && (
              <>
                <div className="processing-spinner" />
                <p className="upload-text">Processing your bookmarks...</p>
                {importedCount > 0 && (
                  <p className="upload-hint">
                    {importedCount} bookmark{importedCount !== 1 ? 's' : ''} imported
                  </p>
                )}
                {importProgress !== null && (
                  <div className="progress-bar">
                    <div
                      className="progress-fill"
                      style={{ width: `${importProgress}%` }}
                    />
                  </div>
                )}
              </>
            )}

            {importStatus === 'success' && (
              <>
                <Check size={32} className="success-icon" />
                <p className="upload-text">
                  Import completed successfully!
                </p>
                <p className="upload-hint">
                  {importedCount} bookmark{importedCount !== 1 ? 's' : ''} imported
                  <br />Previous bookmarks have been replaced
                </p>

                {/* Simple Test Display */}
                <div style={{
                  marginTop: '16px',
                  padding: '12px',
                  backgroundColor: '#f0f9ff',
                  border: '1px solid #0ea5e9',
                  borderRadius: '8px',
                  fontSize: '12px',
                  color: '#0c4a6e'
                }}>
                  🎬 <strong>Multimedia Analysis:</strong> {multimediaAnalysis ? 'Analysis completed!' : 'No analysis data'}
                  {multimediaAnalysis && (
                    <span> - Found {multimediaAnalysis.videos} videos, {multimediaAnalysis.audio} audio, {multimediaAnalysis.documents} documents</span>
                  )}
                </div>

                {/* Debug: Show multimedia analysis state */}
                {console.log('🔍 Rendering multimedia analysis:', multimediaAnalysis)}

                {/* Multimedia Content Analysis */}
                {multimediaAnalysis && (
                  <div style={{
                    marginTop: '20px',
                    padding: '16px',
                    backgroundColor: '#f8fafc',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    textAlign: 'left'
                  }}>
                    <h4 style={{
                      margin: '0 0 12px 0',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#374151',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px'
                    }}>
                      🎬 Multimedia Content Analysis
                    </h4>

                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
                      gap: '12px',
                      fontSize: '12px'
                    }}>
                      {multimediaAnalysis.videos > 0 && (
                        <div style={{
                          padding: '8px',
                          backgroundColor: '#fef2f2',
                          border: '1px solid #fecaca',
                          borderRadius: '6px',
                          textAlign: 'center'
                        }}>
                          <div style={{ fontSize: '16px', marginBottom: '4px' }}>🎥</div>
                          <div style={{ fontWeight: '600', color: '#dc2626' }}>{multimediaAnalysis.videos}</div>
                          <div style={{ color: '#6b7280' }}>Videos</div>
                          {multimediaAnalysis.breakdown.youtube > 0 && (
                            <div style={{ fontSize: '10px', color: '#9ca3af' }}>
                              YouTube: {multimediaAnalysis.breakdown.youtube}
                            </div>
                          )}
                          {multimediaAnalysis.breakdown.vimeo > 0 && (
                            <div style={{ fontSize: '10px', color: '#9ca3af' }}>
                              Vimeo: {multimediaAnalysis.breakdown.vimeo}
                            </div>
                          )}
                        </div>
                      )}

                      {multimediaAnalysis.audio > 0 && (
                        <div style={{
                          padding: '8px',
                          backgroundColor: '#f0fdf4',
                          border: '1px solid #bbf7d0',
                          borderRadius: '6px',
                          textAlign: 'center'
                        }}>
                          <div style={{ fontSize: '16px', marginBottom: '4px' }}>🎵</div>
                          <div style={{ fontWeight: '600', color: '#16a34a' }}>{multimediaAnalysis.audio}</div>
                          <div style={{ color: '#6b7280' }}>Audio</div>
                          {multimediaAnalysis.breakdown.spotify > 0 && (
                            <div style={{ fontSize: '10px', color: '#9ca3af' }}>
                              Spotify: {multimediaAnalysis.breakdown.spotify}
                            </div>
                          )}
                          {multimediaAnalysis.breakdown.soundcloud > 0 && (
                            <div style={{ fontSize: '10px', color: '#9ca3af' }}>
                              SoundCloud: {multimediaAnalysis.breakdown.soundcloud}
                            </div>
                          )}
                        </div>
                      )}

                      {multimediaAnalysis.documents > 0 && (
                        <div style={{
                          padding: '8px',
                          backgroundColor: '#fffbeb',
                          border: '1px solid #fed7aa',
                          borderRadius: '6px',
                          textAlign: 'center'
                        }}>
                          <div style={{ fontSize: '16px', marginBottom: '4px' }}>📄</div>
                          <div style={{ fontWeight: '600', color: '#d97706' }}>{multimediaAnalysis.documents}</div>
                          <div style={{ color: '#6b7280' }}>Documents</div>
                          {multimediaAnalysis.breakdown.pdfs > 0 && (
                            <div style={{ fontSize: '10px', color: '#9ca3af' }}>
                              PDFs: {multimediaAnalysis.breakdown.pdfs}
                            </div>
                          )}
                          {multimediaAnalysis.breakdown.articles > 0 && (
                            <div style={{ fontSize: '10px', color: '#9ca3af' }}>
                              Articles: {multimediaAnalysis.breakdown.articles}
                            </div>
                          )}
                        </div>
                      )}

                      <div style={{
                        padding: '8px',
                        backgroundColor: '#f8fafc',
                        border: '1px solid #e2e8f0',
                        borderRadius: '6px',
                        textAlign: 'center'
                      }}>
                        <div style={{ fontSize: '16px', marginBottom: '4px' }}>🌐</div>
                        <div style={{ fontWeight: '600', color: '#64748b' }}>{multimediaAnalysis.web}</div>
                        <div style={{ color: '#6b7280' }}>Web Pages</div>
                      </div>
                    </div>

                    {multimediaAnalysis.videos > 50 && (
                      <div style={{
                        marginTop: '12px',
                        padding: '12px',
                        backgroundColor: '#fef3c7',
                        border: '1px solid #fbbf24',
                        borderRadius: '6px',
                        fontSize: '12px',
                        color: '#92400e'
                      }}>
                        <div style={{ marginBottom: '8px' }}>
                          💡 <strong>Large video collection detected!</strong> Use the Advanced Video Builder for better organization and playlist creation.
                        </div>
                        {onOpenPanel && (
                          <button
                            onClick={() => {
                              onOpenPanel('multimedia')
                              onClose()
                            }}
                            style={{
                              padding: '6px 12px',
                              backgroundColor: 'var(--accent-color)',
                              color: 'white',
                              border: 'none',
                              borderRadius: '6px',
                              fontSize: '11px',
                              fontWeight: '600',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '4px'
                            }}
                          >
                            🎬 Open Video Builder
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}

                <button
                  onClick={() => {
                    setImportStatus('idle')
                    setImportProgress(null)
                    setImportedCount(0)
                    setMultimediaAnalysis(null)
                    if (fileInputRef.current) {
                      fileInputRef.current.value = ''
                    }
                  }}
                  className="import-another-btn"
                >
                  📁 Import Another File
                </button>
              </>
            )}

            {importStatus === 'error' && (
              <>
                <AlertCircle size={32} className="error-icon" />
                <p className="upload-text">Import failed. Please try again.</p>
              </>
            )}
          </div>
        </div>

        {/* Auto-save Indicator */}
        <div className="import-section">
          <div className="auto-save-indicator disabled">
            <span className="auto-save-icon">💾</span>
            <span className="auto-save-text">Auto-save OFF</span>
          </div>
        </div>

        {/* Templates */}
        <div className="import-section">
          <h3 className="section-title">Need a template?</h3>
          <p className="section-description">
            Download a sample file to see the expected format for your bookmarks.
          </p>
          <div className="template-actions">
            <button
              onClick={() => downloadTemplate('html')}
              className="template-btn"
            >
              <Download size={16} />
              HTML Template
            </button>
            <button
              onClick={() => downloadTemplate('json')}
              className="template-btn"
            >
              <Download size={16} />
              JSON Template
            </button>
            <button
              onClick={() => downloadTemplate('csv')}
              className="template-btn"
            >
              <Download size={16} />
              CSV Template
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="import-section">
          <h3 className="section-title">Export from Browser</h3>
          <div className="browser-instructions">
            <div className="instruction-item">
              <Chrome size={16} />
              <span>Chrome: Settings → Bookmarks → Bookmark manager → Export bookmarks</span>
            </div>
            <div className="instruction-item">
              <Globe size={16} />
              <span>Firefox: Library → Bookmarks → Show All Bookmarks → Export</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
