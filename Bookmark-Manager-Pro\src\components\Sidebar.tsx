import {
  Book<PERSON><PERSON>,
  Brain,
  ChevronDown,
  ChevronRight,
  Clock,
  FileText,
  Folder,
  Globe,
  Layers,
  Menu,
  Play,
  Plus,
  Shield,
  Star,
  Tag,
  Type
} from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'

interface SidebarProps {
  collapsed: boolean
  onCollapse: (collapsed: boolean) => void
  onToggleHealthCheck?: () => void
  onToggleSummaries?: () => void
  onToggleHybrid?: () => void
  onToggleSmartAI?: () => void
  onToggleDomain?: () => void
  onToggleContent?: () => void
  onToggleMultimedia?: () => void
  // Active state props
  healthCheckPanelOpen?: boolean
  summariesPanelOpen?: boolean
  hybridPanelOpen?: boolean
  smartAIPanelOpen?: boolean
  domainPanelOpen?: boolean
  contentPanelOpen?: boolean
  multimediaPanelOpen?: boolean
}

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  onCollapse,
  onToggleHealthCheck,
  onToggleSummaries,
  onToggleHybrid,
  onToggleSmartAI,
  onToggleDomain,
  onToggleContent,
  onToggleMultimedia,
  // Active state props
  healthCheckPanelOpen = false,
  summariesPanelOpen = false,
  hybridPanelOpen = false,
  smartAIPanelOpen = false,
  domainPanelOpen = false,
  contentPanelOpen = false,
  multimediaPanelOpen = false
}) => {
  const {
    bookmarks,
    collections,
    tags,
    playlists,
    selectedCollection,
    setSelectedCollection,
    selectedTags,
    setSelectedTags,
    selectedPlaylist,
    setSelectedPlaylist
  } = useBookmarks()

  // Force glass morphism styles with browser compatibility check
  const sidebarRef = useRef<HTMLElement>(null)

  useEffect(() => {
    if (sidebarRef.current) {
      const sidebar = sidebarRef.current

      // Check if backdrop-filter is supported
      const supportsBackdropFilter = CSS.supports('backdrop-filter', 'blur(1px)') ||
        CSS.supports('-webkit-backdrop-filter', 'blur(1px)')

      console.log('Backdrop filter supported:', supportsBackdropFilter)

      // Force glass morphism styles with highest priority
      sidebar.style.setProperty('background', 'rgba(0, 0, 0, 0.3)', 'important')
      sidebar.style.setProperty('border-right', '2px solid rgba(255, 255, 255, 0.1)', 'important')
      sidebar.style.setProperty('box-shadow', '4px 0 20px rgba(0, 0, 0, 0.05)', 'important')
      sidebar.style.setProperty('color', 'rgba(255, 255, 255, 0.9)', 'important')

      if (supportsBackdropFilter) {
        sidebar.style.setProperty('backdrop-filter', 'blur(12px)', 'important')
        sidebar.style.setProperty('-webkit-backdrop-filter', 'blur(12px)', 'important')
      } else {
        // Fallback for browsers that don't support backdrop-filter
        sidebar.style.setProperty('background', 'rgba(0, 0, 0, 0.8)', 'important')
      }

      // Also force header styles
      const header = sidebar.querySelector('.sidebar-header') as HTMLElement
      if (header) {
        header.style.setProperty('background', 'rgba(0, 0, 0, 0.2)', 'important')
        header.style.setProperty('border-bottom', '2px solid rgba(255, 255, 255, 0.1)', 'important')
        header.style.setProperty('color', 'rgba(255, 255, 255, 0.9)', 'important')

        if (supportsBackdropFilter) {
          header.style.setProperty('backdrop-filter', 'blur(8px)', 'important')
          header.style.setProperty('-webkit-backdrop-filter', 'blur(8px)', 'important')
        }
      }

      // CRITICAL: Force glass morphism on sidebar-content (the main content area)
      const content = sidebar.querySelector('.sidebar-content') as HTMLElement
      if (content) {
        content.style.setProperty('background', 'rgba(0, 0, 0, 0.15)', 'important')
        content.style.setProperty('color', 'rgba(255, 255, 255, 0.9)', 'important')

        if (supportsBackdropFilter) {
          content.style.setProperty('backdrop-filter', 'blur(10px)', 'important')
          content.style.setProperty('-webkit-backdrop-filter', 'blur(10px)', 'important')
        }

        console.log('Glass morphism applied to sidebar-content')

        // Add a blue indicator for content area
        const contentIndicator = document.createElement('div')
        contentIndicator.style.cssText = `
          position: absolute;
          bottom: 5px;
          right: 5px;
          width: 10px;
          height: 10px;
          background: blue;
          border-radius: 50%;
          z-index: 9999;
          pointer-events: none;
        `
        content.appendChild(contentIndicator)

        // Remove indicator after 3 seconds
        setTimeout(() => {
          if (content.contains(contentIndicator)) {
            content.removeChild(contentIndicator)
          }
        }, 3000)
      }

      // Force all child elements to inherit the glass styling
      const allElements = sidebar.querySelectorAll('*')
      allElements.forEach((element) => {
        const el = element as HTMLElement
        if (el.classList.contains('nav-item') || el.classList.contains('section-title')) {
          el.style.setProperty('color', 'rgba(255, 255, 255, 0.9)', 'important')
        }
      })

      console.log('Glass morphism styles applied to sidebar')

      // Add a temporary visual indicator to confirm styles are being applied
      const indicator = document.createElement('div')
      indicator.style.cssText = `
        position: absolute;
        top: 5px;
        right: 5px;
        width: 10px;
        height: 10px;
        background: lime;
        border-radius: 50%;
        z-index: 9999;
        pointer-events: none;
      `
      sidebar.appendChild(indicator)

      // Remove indicator after 3 seconds
      setTimeout(() => {
        if (sidebar.contains(indicator)) {
          sidebar.removeChild(indicator)
        }
      }, 3000)

      // Log computed styles for debugging
      const computedStyles = window.getComputedStyle(sidebar)
      console.log('Sidebar computed styles:', {
        background: computedStyles.background,
        backdropFilter: computedStyles.backdropFilter,
        webkitBackdropFilter: computedStyles.webkitBackdropFilter,
        borderRight: computedStyles.borderRight,
        boxShadow: computedStyles.boxShadow
      })
    }
  }, [])

  // Optimized localization - memory efficient
  const [locale, setLocale] = React.useState<'en-US' | 'en-GB'>(() =>
    localStorage.getItem('bookmark-manager-locale') as 'en-US' | 'en-GB' || 'en-US'
  )

  // Optimized event listener with cleanup
  React.useEffect(() => {
    const handleLocaleChange = (event: CustomEvent) => {
      setLocale(event.detail as 'en-US' | 'en-GB')
    }

    window.addEventListener('localeChanged', handleLocaleChange as EventListener)
    return () => window.removeEventListener('localeChanged', handleLocaleChange as EventListener)
  }, [])

  // Memoized translation function
  const t = React.useCallback((key: string) => {
    const translations = {
      'en-US': { 'sidebar.favorites': 'Favorites', 'sidebar.allBookmarks': 'All Bookmarks', 'sidebar.autoOrganize': 'Auto-organize' },
      'en-GB': { 'sidebar.favorites': 'Favourites', 'sidebar.allBookmarks': 'All Bookmarks', 'sidebar.autoOrganize': 'Auto-organise' }
    }
    return (translations[locale] as any)?.[key] || key
  }, [locale])

  // Removed handleAddBookmarkClick - now using proper panel handlers

  const [collectionsExpanded, setCollectionsExpanded] = useState(true)
  const [tagsExpanded, setTagsExpanded] = useState(true)
  const [playlistsExpanded, setPlaylistsExpanded] = useState(true)
  const [quickActionsExpanded, setQuickActionsExpanded] = useState(true)

  // Collection sorting state (simplified to alphabetical only)
  const [collectionSortOrder, setCollectionSortOrder] = useState<'asc' | 'desc'>('asc')


  const totalBookmarks = bookmarks.length

  // Sort collections alphabetically
  const sortedCollections = [...collections].sort((a, b) => {
    const comparison = a.name.localeCompare(b.name)
    return collectionSortOrder === 'desc' ? -comparison : comparison
  })

  // Toggle alphabetical sort order
  const toggleSortOrder = () => {
    setCollectionSortOrder(collectionSortOrder === 'asc' ? 'desc' : 'asc')
  }
  const favoriteBookmarks = bookmarks.filter(b => b.isFavorite).length
  const recentBookmarks = bookmarks.filter(b => b.isRecentlyAdded).length

  const handleTagToggle = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag]
    setSelectedTags(newTags)
  }

  const getTagCount = (tag: string) => {
    return bookmarks.filter(bookmark => bookmark.tags.includes(tag)).length
  }

  return (
    <aside
      ref={sidebarRef}
      className={`sidebar glass-morphism-sidebar ${collapsed ? 'collapsed' : ''}`}
      style={{
        background: 'rgba(0, 0, 0, 0.3) !important',
        backdropFilter: 'blur(12px) !important',
        WebkitBackdropFilter: 'blur(12px) !important',
        borderRight: '2px solid rgba(255, 255, 255, 0.1) !important',
        boxShadow: '4px 0 20px rgba(0, 0, 0, 0.05) !important',
        color: 'rgba(255, 255, 255, 0.9) !important',
        position: 'relative',
        zIndex: 100
      }}
    >
      <div
        className="sidebar-header glass-morphism-header"
        style={{
          background: 'rgba(0, 0, 0, 0.2) !important',
          backdropFilter: 'blur(8px) !important',
          WebkitBackdropFilter: 'blur(8px) !important',
          borderBottom: '2px solid rgba(255, 255, 255, 0.1) !important',
          color: 'rgba(255, 255, 255, 0.9) !important'
        }}
      >
        <button
          onClick={() => onCollapse(!collapsed)}
          className="collapse-btn"
          aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <Menu size={20} />
        </button>
        {!collapsed && <span className="sidebar-title">Bookmark Studio</span>}
      </div>

      <div
        className="sidebar-content glass-morphism-content"
        style={{
          background: 'rgba(0, 0, 0, 0.15) !important',
          backdropFilter: 'blur(10px) !important',
          WebkitBackdropFilter: 'blur(10px) !important',
          color: 'rgba(255, 255, 255, 0.9) !important',
          flex: 1
        }}
      >
        {/* Library Section */}
        <div className="sidebar-section">
          {!collapsed && (
            <div className="section-header">
              <h3 className="section-title">Library</h3>
              <div className="library-stats">
                <span className="stat">{totalBookmarks} total</span>
                <span className="stat">{collections.length} collections</span>
              </div>
            </div>
          )}

          <nav className="nav-list">
            <button
              onClick={() => setSelectedCollection('all')}
              className={`nav-item ${selectedCollection === 'all' ? 'active' : ''}`}
            >
              <BookOpen size={18} />
              {!collapsed && (
                <>
                  <span>{t('sidebar.allBookmarks')}</span>
                  <div className="nav-item-right">
                    <span className="count">{totalBookmarks}</span>
                  </div>
                </>
              )}
            </button>

            <button
              onClick={() => setSelectedCollection('favorites')}
              className={`nav-item ${selectedCollection === 'favorites' ? 'active' : ''}`}
            >
              <Star size={18} />
              {!collapsed && (
                <>
                  <span>{t('sidebar.favorites')}</span>
                  <div className="nav-item-right">
                    <span className="count">{favoriteBookmarks}</span>
                  </div>
                </>
              )}
            </button>

            <button
              onClick={() => setSelectedCollection('recent')}
              className={`nav-item ${selectedCollection === 'recent' ? 'active' : ''}`}
            >
              <Clock size={18} />
              {!collapsed && (
                <>
                  <span>Recently Added</span>
                  <div className="nav-item-right">
                    <span className="count">{recentBookmarks}</span>
                  </div>
                </>
              )}
            </button>
          </nav>
        </div>

        {/* Collections Section */}
        {!collapsed && (
          <div className="sidebar-section">
            <div className="section-header">
              <button
                onClick={() => setCollectionsExpanded(!collectionsExpanded)}
                className="section-toggle"
              >
                {collectionsExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                <h3 className="section-title">Collections</h3>
              </button>

              {/* Alphabetical Sort Toggle */}
              <div style={{ display: 'flex', gap: '4px' }}>
                <button
                  onClick={toggleSortOrder}
                  className="add-btn"
                  aria-label={`Sort collections ${collectionSortOrder === 'asc' ? 'Z-A' : 'A-Z'}`}
                  title={`Sort collections ${collectionSortOrder === 'asc' ? 'Z-A' : 'A-Z'}`}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '2px',
                    fontSize: '11px',
                    fontWeight: 'bold'
                  }}
                >
                  {collectionSortOrder === 'asc' ? 'A-Z' : 'Z-A'}
                </button>

                <button className="add-btn" aria-label="Add collection">
                  <Plus size={14} />
                </button>
              </div>
            </div>

            {collectionsExpanded && (
              <nav className="nav-list">
                {sortedCollections.map(collection => (
                  <button
                    key={collection.id}
                    onClick={() => setSelectedCollection(collection.name)}
                    className={`nav-item ${selectedCollection === collection.name ? 'active' : ''}`}
                  >
                    <Folder size={18} />
                    <span>{collection.name}</span>
                    <div className="nav-item-right">
                      <span className="count">
                        {collection.count}
                      </span>
                      <span
                        className="collection-indicator"
                        style={{ backgroundColor: collection.color }}
                      />
                    </div>
                  </button>
                ))}
              </nav>
            )}
          </div>
        )}

        {/* Tags Section */}
        {!collapsed && (
          <div className="sidebar-section">
            <div className="section-header">
              <button
                onClick={() => setTagsExpanded(!tagsExpanded)}
                className="section-toggle"
              >
                {tagsExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                <h3 className="section-title">Tags</h3>
              </button>
            </div>

            {tagsExpanded && (
              <div className="tags-list">
                {tags.slice(0, 15).map(tag => {
                  const count = getTagCount(tag)
                  const isSelected = selectedTags.includes(tag)

                  return (
                    <button
                      key={tag}
                      onClick={() => handleTagToggle(tag)}
                      className={`tag-item ${isSelected ? 'selected' : ''}`}
                    >
                      <Tag size={14} />
                      <span className="tag-name">{tag}</span>
                      <span className="tag-count">{count}</span>
                    </button>
                  )
                })}

                {tags.length > 15 && (
                  <button className="show-more-tags">
                    Show {tags.length - 15} more...
                  </button>
                )}
              </div>
            )}
          </div>
        )}

        {/* Playlists Section */}
        {!collapsed && (
          <div className="sidebar-section">
            <div className="section-header">
              <button
                onClick={() => setPlaylistsExpanded(!playlistsExpanded)}
                className="section-toggle"
              >
                {playlistsExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                <h3 className="section-title">Playlists</h3>
              </button>
              <button className="add-btn" aria-label="Add playlist">
                <Plus size={14} />
              </button>
            </div>

            {playlistsExpanded && (
              <nav className="nav-list">
                {playlists.length === 0 ? (
                  <div className="empty-playlists-message">
                    <p>No playlists yet</p>
                  </div>
                ) : (
                  playlists.map(playlist => (
                    <button
                      key={playlist.id}
                      onClick={() => setSelectedPlaylist(selectedPlaylist === playlist.id ? null : playlist.id)}
                      className={`nav-item ${selectedPlaylist === playlist.id ? 'active' : ''}`}
                    >
                      <Play size={18} />
                      <span>{playlist.name}</span>
                      <div className="nav-item-right">
                        <span className="count">
                          {playlist.bookmarkIds.length}
                        </span>
                        <span
                          className="collection-indicator"
                          style={{ backgroundColor: playlist.color }}
                        />
                      </div>
                    </button>
                  ))
                )}
              </nav>
            )}
          </div>
        )}

        {/* Quick Actions Section - Expanded */}
        {!collapsed && (
          <div className="sidebar-section">
            <div className="section-header">
              <button
                onClick={() => setQuickActionsExpanded(!quickActionsExpanded)}
                className="section-toggle"
              >
                {quickActionsExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                <h3 className="section-title">Bookmark Tools</h3>
              </button>
            </div>

            {quickActionsExpanded && (
              <div className="quick-actions">
                <button
                  className={`action-item ${healthCheckPanelOpen ? 'active' : ''}`}
                  onClick={onToggleHealthCheck}
                  title="Open Bookmark Health Check panel"
                >
                  <Shield size={16} />
                  <span>Check Bookmark Health</span>
                </button>
                <button
                  className={`action-item ${summariesPanelOpen ? 'active' : ''}`}
                  onClick={onToggleSummaries}
                  title="Open Generate Summaries panel"
                >
                  <FileText size={16} />
                  <span>Generate Summaries</span>
                </button>
                <button
                  className={`action-item ${hybridPanelOpen ? 'active' : ''}`}
                  onClick={onToggleHybrid}
                  title="Open Hybrid Organization panel"
                >
                  <Layers size={16} />
                  <span>Hybrid</span>
                </button>
                <button
                  className={`action-item ${smartAIPanelOpen ? 'active' : ''}`}
                  onClick={onToggleSmartAI}
                  title="Open Smart AI Organization panel (requires Gemini API key)"
                >
                  <Brain size={16} />
                  <span>Smart AI</span>
                </button>
                <button
                  className={`action-item ${domainPanelOpen ? 'active' : ''}`}
                  onClick={onToggleDomain}
                  title="Open Domain Organization panel"
                >
                  <Globe size={16} />
                  <span>Domain</span>
                </button>
                <button
                  className={`action-item ${contentPanelOpen ? 'active' : ''}`}
                  onClick={onToggleContent}
                  title="Open Content Organization panel"
                >
                  <Type size={16} />
                  <span>Content</span>
                </button>
                <button
                  className={`action-item ${multimediaPanelOpen ? 'active' : ''}`}
                  onClick={onToggleMultimedia}
                  title="Open Multimedia Playlists panel"
                >
                  <Play size={16} />
                  <span>Multimedia</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Quick Actions Section - Collapsed (Icons Only) */}
        {collapsed && (
          <div className="sidebar-section">
            <div className="collapsed-tools">
              <button
                className={`collapsed-tool-btn ${healthCheckPanelOpen ? 'active' : ''}`}
                onClick={onToggleHealthCheck}
                title="Bookmark Health Check"
              >
                <Shield size={20} />
              </button>
              <button
                className={`collapsed-tool-btn ${summariesPanelOpen ? 'active' : ''}`}
                onClick={onToggleSummaries}
                title="Generate Summaries"
              >
                <FileText size={20} />
              </button>
              <button
                className={`collapsed-tool-btn ${hybridPanelOpen ? 'active' : ''}`}
                onClick={onToggleHybrid}
                title="Hybrid Organization"
              >
                <Layers size={20} />
              </button>
              <button
                className={`collapsed-tool-btn ${smartAIPanelOpen ? 'active' : ''}`}
                onClick={onToggleSmartAI}
                title="Smart AI Organization"
              >
                <Brain size={20} />
              </button>
              <button
                className={`collapsed-tool-btn ${domainPanelOpen ? 'active' : ''}`}
                onClick={onToggleDomain}
                title="Domain Organization"
              >
                <Globe size={20} />
              </button>
              <button
                className={`collapsed-tool-btn ${contentPanelOpen ? 'active' : ''}`}
                onClick={onToggleContent}
                title="Content Organization"
              >
                <Type size={20} />
              </button>
              <button
                className={`collapsed-tool-btn ${multimediaPanelOpen ? 'active' : ''}`}
                onClick={onToggleMultimedia}
                title="Multimedia Playlists"
              >
                <Play size={20} />
              </button>
            </div>
          </div>
        )}
      </div>
    </aside>
  )
}
