/* Import Mind Map Styles */
@import './styles/mindmap.css';

/* Import Multimedia Integration Styles */
@import './styles/multimedia-integration.css';
@import './styles/multimedia-design-system.css';

/* Import Optimized Panel Styles */
@import './styles/optimized-panels.css';

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color Scheme */
  --primary-bg: #1a1d23;
  --secondary-bg: #242832;
  --tertiary-bg: #2d3139;
  --accent-color: #4a9eff;
  --accent-color-rgb: 74, 158, 255;
  --accent-hover: #3d8bea;
  --text-primary: #ffffff;
  --text-secondary: #b4b8c0;
  --text-muted: #8a8f98;
  --border-color: #2d3139;
  --border-hover: #3a3f4a;
  --success-color: #22c55e;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --purple-color: #8b5cf6;
  --cyan-color: #06b6d4;

  /* Spacing */
  --sidebar-width: 320px;
  --sidebar-collapsed-width: 64px;
  --header-height: 72px;
  --padding-sm: 8px;
  --padding-md: 16px;
  --padding-lg: 24px;
  --padding-xl: 32px;

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-medium: 0.25s ease-out;
  --transition-slow: 0.4s ease-out;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;
}

/* Classic Theme CSS Variable Overrides */
.theme-classic {
  /* Light color scheme for Classic theme */
  --primary-bg: #ffffff;
  --secondary-bg: #f8fafc;
  --tertiary-bg: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --border-color: #cbd5e1;
  --border-hover: #94a3b8;

  /* Keep the same accent colors but adjust for light theme */
  --accent-color: #3b82f6;
  --accent-hover: #2563eb;

  /* Adjust shadows for light theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.5;
}

.bookmark-manager {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Sidebar Layout Styles - Visual styles handled by glass morphism section below */
.sidebar {
  width: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-medium);
  position: relative;
  z-index: 100;
  /* Background, border, and color handled by glass morphism styles */
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: var(--padding-lg);
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  min-height: var(--header-height);
  width: 100%;
  height: auto;
  max-width: 100%;
  /* Background, border, and color handled by glass morphism styles */
}

.collapse-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.collapse-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  opacity: 1;
  transition: opacity var(--transition-medium);
}

.sidebar.collapsed .sidebar-title {
  opacity: 0;
}

.sidebar.collapsed .sidebar-header {
  justify-content: center;
  padding: var(--padding-lg) var(--padding-sm);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--padding-md);
}

.sidebar-section {
  margin-bottom: var(--padding-xl);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--padding-md);
  padding: 0 var(--padding-sm);
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.library-stats {
  display: flex;
  gap: var(--padding-sm);
  font-size: 12px;
  color: var(--text-muted);
}

.section-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.section-toggle:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.add-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.add-btn:hover {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.nav-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 6px var(--padding-sm);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  font-size: 13px;
  text-align: left;
  width: 100%;
  position: relative;
}

.nav-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.nav-item .count {
  background-color: var(--tertiary-bg);
  color: var(--text-muted);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
  text-align: center;
  /* Center align the count text */
  min-width: 24px;
  /* Ensure consistent width */
}

.nav-item.active .count {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

/* Nav item right side container for count and color indicator */
.nav-item-right {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

.collection-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
  display: inline-block;
}

.tags-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 6px var(--padding-sm);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  font-size: 13px;
  text-align: left;
  width: 100%;
}

.tag-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.tag-item.selected {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.tag-name {
  flex: 1;
  text-align: left;
}

.tag-count {
  font-size: 11px;
  color: var(--text-muted);
  background-color: var(--tertiary-bg);
  padding: 1px 4px;
  border-radius: 8px;
}

.tag-item.selected .tag-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

.show-more-tags {
  background: none;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  padding: var(--padding-sm);
  font-size: 13px;
  text-align: left;
  transition: color var(--transition-fast);
}

.show-more-tags:hover {
  color: var(--accent-hover);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: var(--padding-md);
  padding: var(--padding-md);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 14px;
  text-align: left;
  width: 100%;
}

.action-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.action-item.active {
  background-color: var(--accent-color);
  color: var(--text-primary);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Collapsed bookmark tools */
.collapsed-tools {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.collapsed-tool-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  border-radius: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin: 0 auto;
}

.collapsed-tool-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.collapsed-tool-btn.active {
  background-color: var(--accent-color);
  color: var(--text-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Main Content Styles with Enhanced Glass Morphism */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 0;
  transition: margin-left var(--transition-medium);
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  border-left: 2px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.05) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Main Content - Light Themes */
[data-theme*="light"] .main-content {
  background: rgba(255, 255, 255, 0.95) !important;
  border-left: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

.main-content.sidebar-collapsed {
  margin-left: 0;
}

/* Enhanced Glass Morphism Header */
.header {
  height: var(--header-height);
  background: rgba(0, 0, 0, 0.3) !important;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05) !important;
  display: flex;
  align-items: center;
  padding: 0 24px;
  gap: 24px;
  position: relative;
  z-index: 1200;
}

/* Header - Light Themes */
[data-theme*="light"] .header {
  background: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.header-center {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: center;
  max-width: 800px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.filter-buttons {
  display: flex;
  gap: 2px;
  background: var(--tertiary-bg);
  padding: 4px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}



.header-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.bookmark-count {
  color: var(--text-muted);
  font-size: 14px;
}

/* Small NEW indicator for right corner */
.new-indicator-small {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: var(--accent-color);
  color: white;
  font-size: 9px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
}

.save-btn {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  font-size: 11px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  gap: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.save-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.save-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.save-btn.saving {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.save-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes pulse-badge {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(74, 158, 255, 0.3);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(74, 158, 255, 0.4);
  }
}

.header-center {
  flex: 0 1 600px;
  display: flex;
  align-items: center;
  gap: var(--padding-lg);
  justify-content: center;
  max-width: 600px;
  margin-right: var(--padding-lg);
  overflow: hidden;
  position: relative;
  z-index: 1300;
  pointer-events: auto;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: var(--radius-lg);
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 14px;
  transition: all var(--transition-fast);
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Search Input - Light Themes */
[data-theme*="light"] .search-input {
  background: rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.1);
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
}

.search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 20px;
  padding: 2px;
  border-radius: 50%;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.filter-buttons {
  display: flex;
  gap: 4px;
  background-color: var(--tertiary-bg);
  padding: 4px;
  border-radius: var(--radius-lg);
}

.filter-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 13px;
  font-weight: 500;
}

.filter-btn:hover {
  color: var(--text-primary);
}

.filter-btn.active {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--padding-md);
  flex-shrink: 0;
  position: relative;
  z-index: 200;
  min-width: fit-content;
}

.import-btn {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 10px 16px;
  background-color: var(--accent-color);
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
}

.import-btn:hover {
  background-color: var(--accent-hover);
}

.import-btn.active {
  background-color: var(--accent-hover);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.action-btn:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

/* Content Wrapper with Enhanced Glass Morphism */
.content-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border-radius: 8px;
  margin: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Content Wrapper - Light Themes */
[data-theme*="light"] .content-wrapper {
  background: rgba(255, 255, 255, 0.8) !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

/* Bookmark Grid Styles with Enhanced Glass Morphism */
.bookmark-grid-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--padding-xl);
  background: rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border-radius: 12px;
  margin: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Bookmark Grid Container - Light Themes */
[data-theme*="light"] .bookmark-grid-container {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
}

.grid-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--padding-xl);
  padding: var(--padding-md) var(--padding-lg);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  gap: var(--padding-md);
  background: rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(6px) !important;
  -webkit-backdrop-filter: blur(6px) !important;
  border-radius: 8px 8px 0 0;
  color: rgba(255, 255, 255, 0.95) !important;
}

/* Grid Header - Light Themes */
[data-theme*="light"] .grid-header {
  background: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.9) !important;
}

/* Multimedia Quick Actions with Glass Morphism */
.multimedia-quick-actions {
  display: flex;
  gap: 8px;
  padding: 12px 0;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
  margin-bottom: 16px;
  background: rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(6px) !important;
  -webkit-backdrop-filter: blur(6px) !important;
  border-radius: 8px;
  padding: 12px 16px;
}

/* Multimedia Quick Actions - Light Themes */
[data-theme*="light"] .multimedia-quick-actions {
  border-bottom: 2px solid rgba(0, 0, 0, 0.1) !important;
  background: rgba(255, 255, 255, 0.6) !important;
}

/* Multimedia Action Buttons with Glass Morphism */
.multimedia-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 14px;
  background: rgba(0, 0, 0, 0.2) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.multimedia-action-btn:hover {
  background: rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Multimedia Action Buttons - Light Themes */
[data-theme*="light"] .multimedia-action-btn {
  background: rgba(255, 255, 255, 0.8) !important;
  color: rgba(0, 0, 0, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

[data-theme*="light"] .multimedia-action-btn:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

/* Grid Title and Subtitle with Glass Morphism */
.grid-title {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 600;
  font-size: 1.5rem;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.grid-subtitle {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 0.9rem;
  margin: 4px 0 0 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Grid Title and Subtitle - Light Themes */
[data-theme*="light"] .grid-title {
  color: rgba(0, 0, 0, 0.9) !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
}

[data-theme*="light"] .grid-subtitle {
  color: rgba(0, 0, 0, 0.7) !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

/* Memory Optimization Indicators */
.memory-optimization-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 4px;
  font-size: 0.75rem;
  margin-left: 8px;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

[data-theme*="light"] .memory-optimization-indicator {
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Optimization Suggestion Toast with Glass Morphism */
.optimization-suggestion {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  animation: slideInUp 0.3s ease-out;
}

.suggestion-content {
  background: rgba(0, 0, 0, 0.3) !important;
  border: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.suggestion-close {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

.suggestion-close:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

/* Optimization Suggestion - Light Themes */
[data-theme*="light"] .suggestion-content {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

[data-theme*="light"] .suggestion-close {
  background: rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  color: rgba(0, 0, 0, 0.8) !important;
}

[data-theme*="light"] .suggestion-close:hover {
  background: rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(0, 0, 0, 0.3) !important;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.loading-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--padding-xl);
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 14px;
  background: rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(6px) !important;
  -webkit-backdrop-filter: blur(6px) !important;
  border-radius: 8px;
  margin: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Loading Placeholder - Light Themes */
[data-theme*="light"] .loading-placeholder {
  color: rgba(0, 0, 0, 0.7) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Removed duplicate grid-title and grid-subtitle styles - using enhanced glass morphism versions above */

.bookmark-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--padding-lg);
  padding-bottom: var(--padding-xl);
}

/* Bookmark Card Styles */
.bookmark-card {
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--padding-lg);
  cursor: pointer;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

/* Selection Mode Styles */
.bookmark-card.select-mode {
  cursor: default;
}

.bookmark-card.selected {
  border-color: var(--accent-color) !important;
  background-color: rgba(74, 158, 255, 0.1) !important;
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2) !important;
  transform: scale(1.02);
}

.selection-indicator {
  position: absolute;
  top: 12px;
  left: 12px;
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  background-color: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  z-index: 10;
}

.selection-indicator:hover {
  border-color: var(--accent-color);
  background-color: var(--tertiary-bg);
}

.selection-indicator.selected {
  border-color: var(--accent-color);
  background-color: var(--accent-color);
  color: white;
}

.bookmark-card:hover,
.bookmark-card:focus,
.bookmark-card:focus-visible {
  transform: translateY(-2px) rotate(-1deg) !important;
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
  z-index: 1;
}

.bookmark-card:nth-child(even):hover,
.bookmark-card:nth-child(even):focus,
.bookmark-card:nth-child(even):focus-visible {
  transform: translateY(-2px) rotate(1deg) !important;
}

.bookmark-card.dragging {
  opacity: 0.6;
  transform: rotate(5deg);
}

.bookmark-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--padding-md);
}

.bookmark-favicon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--tertiary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.favicon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.favicon-fallback {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.favicon-icon {
  font-size: 18px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.bookmark-actions {
  display: flex;
  gap: var(--padding-xs);
  opacity: 0;
  transition: opacity var(--transition-medium);
  align-items: center;
}

.bookmark-card:hover .bookmark-actions {
  opacity: 1;
}

/* Show both favorite button and menu container */
.bookmark-actions .favorite-btn {
  display: flex;
}

.bookmark-actions .menu-container {
  display: flex;
}

.favorite-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 6px;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.favorite-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--warning-color);
}

.favorite-btn.active {
  color: var(--warning-color);
}

.favorite-btn svg {
  width: 20px;
  height: 20px;
  stroke-width: 1.5;
}

.menu-container {
  position: relative;
}

.menu-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--padding-sm);
  min-width: 150px;
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-sm) var(--padding-md);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 14px;
  text-align: left;
  width: 100%;
}

.menu-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.menu-item.danger {
  color: var(--error-color);
}

.menu-item.danger:hover {
  background-color: var(--error-color);
  color: var(--text-primary);
}

.menu-item.favorite-active {
  color: var(--accent-color);
}

.menu-item.favorite-active svg {
  fill: var(--accent-color);
  stroke: var(--accent-color);
}

.menu-item svg {
  flex-shrink: 0;
  width: 14px;
  height: 14px;
  stroke: currentColor;
}

.menu-item.favorite-active svg {
  fill: var(--accent-color);
  stroke: var(--accent-color);
}

.menu-separator {
  margin: var(--padding-sm) 0;
  border: none;
  border-top: 1px solid var(--border-color);
}

.bookmark-content {
  margin-bottom: var(--padding-lg);
}

.bookmark-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--padding-sm);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bookmark-description {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: var(--padding-md);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bookmark-url {
  color: var(--text-muted);
  font-size: 13px;
  font-weight: 500;
}

.bookmark-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  gap: var(--padding-md);
}

.bookmark-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  flex: 1;
}

.tag {
  background-color: var(--tertiary-bg);
  color: var(--text-muted);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.tag-more {
  background-color: var(--tertiary-bg);
  color: var(--text-muted);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.bookmark-meta {
  display: flex;
  gap: var(--padding-md);
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-muted);
  font-size: 12px;
}

.bookmark-collection-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity var(--transition-medium);
  z-index: 5;
  pointer-events: none;
}

.bookmark-card:hover .bookmark-collection-indicator {
  opacity: 1;
}

.collection-badge {
  background-color: rgba(var(--accent-color-rgb), 0.9);
  color: var(--text-primary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  /* Center align the collection text */
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Skeleton Styles */
.bookmark-card.skeleton {
  pointer-events: none;
}

.skeleton-avatar {
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-btn {
  width: 32px;
  height: 32px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

.skeleton-title {
  height: 20px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
  margin-bottom: var(--padding-sm);
}

.skeleton-description {
  margin-bottom: var(--padding-md);
}

.skeleton-line {
  height: 14px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
  margin-bottom: 4px;
}

.skeleton-line.short {
  width: 70%;
}

.skeleton-url {
  height: 12px;
  width: 60%;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
  margin-bottom: var(--padding-lg);
}

.skeleton-tag {
  height: 16px;
  width: 40px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 10px;
}

.skeleton-meta {
  height: 12px;
  width: 30px;
  background: linear-gradient(90deg, var(--tertiary-bg) 0%, var(--border-color) 50%, var(--tertiary-bg) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* Empty State Styles with Glass Morphism */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--padding-xl) * 2;
  height: 100%;
  background: rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Empty State - Light Themes */
[data-theme*="light"] .empty-state {
  background: rgba(255, 255, 255, 0.5) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.8) !important;
}

.empty-icon {
  color: var(--text-muted);
  margin-bottom: var(--padding-lg);
  opacity: 0.6;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--padding-md);
}

.empty-description {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: var(--padding-xl);
  max-width: 400px;
}

.empty-actions {
  display: flex;
  gap: var(--padding-md);
  flex-wrap: wrap;
  justify-content: center;
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 12px 24px;
  background-color: var(--accent-color);
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
}

.btn-primary:hover {
  background-color: var(--accent-hover);
}

.btn-secondary {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: 12px 24px;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
}

.btn-secondary:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

/* Legacy import-panel styles removed - now handled by unified design system in optimized-panels.css */

/* THEME-RESPONSIVE HEADER DESIGN - MODERN STRUCTURE WITH ADAPTIVE COLORS */
.import-header,
.export-header,
.organization-header,
.panel-header {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  /* Default modern styling */
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* CLASSIC THEME HEADER - Use contrasting colors */
.theme-classic .import-header,
.theme-classic .export-header,
.theme-classic .organization-header,
.theme-classic .panel-header {
  background: #e2e8f0;
  border-bottom: 2px solid #cbd5e1;
  color: #1e293b;
  /* Remove glass effect for classic mode */
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  /* Ensure visibility */
  opacity: 1;
  visibility: visible;
  display: flex;
}

/* UNIFIED GLASS MORPHISM HEADER with High Specificity */
.import-header,
.export-header,
.organization-header,
.panel-header {
  background: rgba(0, 0, 0, 0.2) !important;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* UNIFIED GLASS MORPHISM HEADER - LIGHT THEMES with High Specificity */
[data-theme*="light"] .import-header,
[data-theme*="light"] .export-header,
[data-theme*="light"] .organization-header,
[data-theme*="light"] .panel-header {
  background: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

/* THEME-RESPONSIVE TITLE DESIGN - MODERN STRUCTURE WITH ADAPTIVE COLORS */
.import-title,
.export-title,
.organization-title,
.panel-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  /* Default modern styling */
  color: rgba(255, 255, 255, 0.95);
}

/* CLASSIC THEME TITLE - Use actual theme text color */
.theme-classic .import-title,
.theme-classic .export-title,
.theme-classic .organization-title,
.theme-classic .panel-title {
  color: var(--text-primary, #1e293b) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* UNIFIED GLASS MORPHISM TITLES */
.import-title,
.export-title,
.organization-title,
.panel-title {
  color: rgba(255, 255, 255, 0.95);
}

/* UNIFIED GLASS MORPHISM TITLES - LIGHT THEMES */
[data-theme*="light"] .import-title,
[data-theme*="light"] .export-title,
[data-theme*="light"] .organization-title,
[data-theme*="light"] .panel-title {
  color: rgba(0, 0, 0, 0.9);
}

/* THEME-RESPONSIVE CLOSE BUTTON - MODERN STRUCTURE WITH ADAPTIVE COLORS */
.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Default modern styling */
  color: rgba(255, 255, 255, 0.7);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.95);
}

/* CLASSIC THEME CLOSE BUTTON - Use actual theme colors */
.theme-classic .close-btn {
  color: var(--text-secondary);
}

.theme-classic .close-btn:hover {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

/* UNIFIED GLASS MORPHISM CLOSE BUTTON */
.close-btn {
  color: rgba(255, 255, 255, 0.7);
}

.theme-modern .close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.95);
}

/* MODERN THEME LIGHT MODE - Adjust colors */
.theme-modern.light .close-btn {
  color: rgba(0, 0, 0, 0.6);
}

.theme-modern.light .close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.9);
}

/* Legacy close button hover rule removed - handled by unified design above */

/* UNIFIED CONTENT STYLING - ALWAYS MODERN */
.import-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.import-section {
  margin-bottom: 24px;
}

/* THEME-RESPONSIVE SECTION STYLING - MODERN STRUCTURE WITH ADAPTIVE COLORS */
.import-section .section-title,
.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  text-transform: none;
  letter-spacing: normal;
  /* Default modern styling */
  color: rgba(255, 255, 255, 0.9);
}

.section-description {
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.4;
  /* Default modern styling */
  color: rgba(255, 255, 255, 0.7);
}

/* CLASSIC THEME SECTION - Use actual theme colors */
.theme-classic .import-section .section-title,
.theme-classic .section-title {
  color: var(--text-primary, #1e293b) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.theme-classic .section-description {
  color: var(--text-secondary, #64748b) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* UNIFIED GLASS MORPHISM SECTION TITLES with High Specificity */
.sidebar .section-title,
.import-section .section-title,
.section-title {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Section Titles - Light Themes */
[data-theme*="light"] .sidebar .section-title,
[data-theme*="light"] .import-section .section-title,
[data-theme*="light"] .section-title {
  color: rgba(0, 0, 0, 0.9) !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;
}

.theme-modern .section-description {
  color: rgba(255, 255, 255, 0.7);
}

/* MODERN THEME LIGHT MODE - Adjust colors */
.theme-modern.light .import-section .section-title,
.theme-modern.light .section-title {
  color: rgba(0, 0, 0, 0.8);
}

.theme-modern.light .section-description {
  color: rgba(0, 0, 0, 0.6);
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

/* THEME-RESPONSIVE FORMAT OPTION STYLING - MODERN STRUCTURE WITH ADAPTIVE COLORS */
.format-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
  /* Default modern styling */
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.format-option:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.95);
}

.format-option.active {
  background: rgba(74, 158, 255, 0.3);
  border-color: #4A9EFF;
  color: rgba(255, 255, 255, 0.95);
}

/* CLASSIC THEME FORMAT OPTION - Use contrasting colors */
.theme-classic .format-option {
  background: #ffffff !important;
  border: 2px solid #cbd5e1 !important;
  color: #1e293b !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.theme-classic .format-option:hover {
  background: #f8fafc !important;
  border-color: #94a3b8 !important;
  color: #1e293b !important;
}

.theme-classic .format-option.active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* ========================================
   UNIFIED SIDEBAR STYLING
   ======================================== */

/* SIDEBAR - Main Container with Glass Morphism - HIGHEST PRIORITY */
.bookmark-manager .sidebar,
aside.sidebar,
.sidebar {
  background: rgba(0, 0, 0, 0.3) !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-right: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  opacity: 1 !important;
  visibility: visible !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05) !important;
  /* Override any theme or CSS variable backgrounds */
  --secondary-bg: transparent !important;
  --surface-100: transparent !important;
  --sidebar-background: transparent !important;
}

/* Sidebar - Light Themes - HIGHEST PRIORITY */
[data-theme*="light"] .bookmark-manager .sidebar,
[data-theme*="light"] aside.sidebar,
[data-theme*="light"] .sidebar {
  background: rgba(255, 255, 255, 0.95) !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  border-right: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
  /* Override any theme or CSS variable backgrounds */
  --secondary-bg: transparent !important;
  --surface-100: transparent !important;
  --sidebar-background: transparent !important;
}

/* SIDEBAR HEADER with Glass Morphism - HIGHEST PRIORITY */
.bookmark-manager .sidebar .sidebar-header,
.sidebar .sidebar-header,
.sidebar-header {
  background: rgba(0, 0, 0, 0.2) !important;
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  opacity: 1 !important;
  visibility: visible !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

/* Sidebar Header - Light Themes - HIGHEST PRIORITY */
[data-theme*="light"] .bookmark-manager .sidebar .sidebar-header,
[data-theme*="light"] .sidebar .sidebar-header,
[data-theme*="light"] .sidebar-header {
  background: rgba(255, 255, 255, 0.8) !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

/* SIDEBAR TITLE with Glass Morphism */
.sidebar-title {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 600;
  opacity: 1;
  visibility: visible;
}

/* Sidebar Title - Light Themes */
[data-theme*="light"] .sidebar-title {
  color: rgba(0, 0, 0, 0.9) !important;
}

/* SIDEBAR CONTENT with Glass Morphism */
.sidebar-content {
  background: transparent;
  color: rgba(255, 255, 255, 0.9) !important;
  opacity: 1;
  visibility: visible;
  padding: var(--padding-md);
  overflow-y: auto;
  flex: 1;
}

/* Sidebar Content - Light Themes */
[data-theme*="light"] .sidebar-content {
  color: rgba(0, 0, 0, 0.9) !important;
}

/* SIDEBAR SECTIONS */
.sidebar-section {
  opacity: 1;
  visibility: visible;
}

.section-header {
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
}

.section-title {
  color: var(--text-primary);
  font-weight: 600;
  opacity: 1;
  visibility: visible;
}

/* NAVIGATION ITEMS */
.nav-list {
  opacity: 1;
  visibility: visible;
}

/* Enhanced Navigation Items with Glass Morphism */
.sidebar .nav-item,
.nav-item {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  opacity: 1 !important;
  visibility: visible !important;
  margin-bottom: 6px !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  backdrop-filter: blur(6px) !important;
  -webkit-backdrop-filter: blur(6px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Navigation Items - Light Themes */
[data-theme*="light"] .nav-item {
  background: rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

/* Enhanced Navigation Items Hover States */
.sidebar .nav-item:hover,
.nav-item:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  color: rgba(255, 255, 255, 0.98) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Navigation Items Hover - Light Themes */
[data-theme*="light"] .sidebar .nav-item:hover,
[data-theme*="light"] .nav-item:hover {
  background: rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
  color: rgba(0, 0, 0, 0.98) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.nav-item.active {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

.nav-item .count {
  background: var(--border-color);
  color: var(--text-secondary);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
}

.nav-item.active .count {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* LIBRARY STATS */
.library-stats {
  opacity: 1;
  visibility: visible;
}

.library-stats .stat {
  color: var(--text-secondary);
  font-size: 12px;
  opacity: 1;
  visibility: visible;
}

/* COLLECTIONS */
.collection-item {
  background: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  opacity: 1;
  visibility: visible;
  margin-bottom: 2px;
  border-radius: 4px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
}

/* Collection Items - Light Themes */
[data-theme*="light"] .collection-item {
  background: rgba(0, 0, 0, 0.03) !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

.collection-item:hover {
  background: rgba(255, 255, 255, 0.12) !important;
  border-color: rgba(255, 255, 255, 0.25) !important;
}

/* Collection Items Hover - Light Themes */
[data-theme*="light"] .collection-item:hover {
  background: rgba(0, 0, 0, 0.06) !important;
  border-color: rgba(0, 0, 0, 0.12) !important;
}

.collection-item.active {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

/* CLASSIC THEME TAGS */
.theme-classic .tag-item {
  background: #f1f5f9 !important;
  border: 1px solid #cbd5e1 !important;
  color: #475569 !important;
  opacity: 1 !important;
  visibility: visible !important;
  border-radius: 16px !important;
  padding: 4px 12px !important;
  margin: 2px !important;
  font-size: 12px !important;
  transition: all 0.2s ease !important;
}

.theme-classic .tag-item:hover {
  background: #e2e8f0 !important;
  border-color: #94a3b8 !important;
}

.theme-classic .tag-item.selected {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* CLASSIC THEME BUTTONS */
.theme-classic .collapse-btn {
  background: #ffffff !important;
  border: 1px solid #cbd5e1 !important;
  color: #1e293b !important;
  opacity: 1 !important;
  visibility: visible !important;
  border-radius: 6px !important;
  padding: 8px !important;
  transition: all 0.2s ease !important;
}

.theme-classic .collapse-btn:hover {
  background: #f8fafc !important;
  border-color: #94a3b8 !important;
}

/* CLASSIC THEME QUICK ACTIONS */
.theme-classic .quick-actions {
  opacity: 1 !important;
  visibility: visible !important;
}

.theme-classic .action-item {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  color: #1e293b !important;
  opacity: 1 !important;
  visibility: visible !important;
  margin-bottom: 4px !important;
  border-radius: 6px !important;
  padding: 10px 12px !important;
  transition: all 0.2s ease !important;
}

.theme-classic .action-item:hover {
  background: #f8fafc !important;
  border-color: #cbd5e1 !important;
}

.theme-classic .action-item.active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* CLASSIC THEME COLLAPSED TOOLS */
.theme-classic .collapsed-tools {
  opacity: 1 !important;
  visibility: visible !important;
}

.theme-classic .collapsed-tool-btn {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  color: #1e293b !important;
  opacity: 1 !important;
  visibility: visible !important;
  margin-bottom: 4px !important;
  border-radius: 6px !important;
  padding: 8px !important;
  transition: all 0.2s ease !important;
}

.theme-classic .collapsed-tool-btn:hover {
  background: #f8fafc !important;
  border-color: #cbd5e1 !important;
}

.theme-classic .collapsed-tool-btn.active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* CLASSIC THEME - Ensure all sidebar content is visible */
.theme-classic .sidebar * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* CLASSIC THEME - Override any conflicting styles */
.theme-classic .sidebar .nav-item-right {
  opacity: 1 !important;
  visibility: visible !important;
}

.theme-classic .sidebar .nav-item-right .count {
  opacity: 1 !important;
  visibility: visible !important;
}

/* CLASSIC THEME SECTION TOGGLES AND BUTTONS */
.theme-classic .section-toggle {
  background: transparent !important;
  border: none !important;
  color: #1e293b !important;
  opacity: 1 !important;
  visibility: visible !important;
  padding: 4px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.theme-classic .section-toggle:hover {
  background: #f1f5f9 !important;
}

.theme-classic .add-btn {
  background: #ffffff !important;
  border: 1px solid #cbd5e1 !important;
  color: #1e293b !important;
  opacity: 1 !important;
  visibility: visible !important;
  border-radius: 4px !important;
  padding: 6px !important;
  transition: all 0.2s ease !important;
}

.theme-classic .add-btn:hover {
  background: #f8fafc !important;
  border-color: #94a3b8 !important;
}

/* CLASSIC THEME COLLECTION ITEMS */
.theme-classic .collection-item {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  color: #1e293b !important;
  opacity: 1 !important;
  visibility: visible !important;
  margin-bottom: 2px !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  transition: all 0.2s ease !important;
}

.theme-classic .collection-item:hover {
  background: #f8fafc !important;
  border-color: #cbd5e1 !important;
}

.theme-classic .collection-item.active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

.theme-classic .collection-item .collection-count {
  background: #e2e8f0 !important;
  color: #64748b !important;
  border-radius: 12px !important;
  padding: 2px 6px !important;
  font-size: 11px !important;
  font-weight: 500 !important;
}

.theme-classic .collection-item.active .collection-count {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

/* CLASSIC THEME TAGS LIST */
.theme-classic .tags-list {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 4px !important;
}

.theme-classic .tag-item {
  background: #f1f5f9 !important;
  border: 1px solid #cbd5e1 !important;
  color: #475569 !important;
  opacity: 1 !important;
  visibility: visible !important;
  border-radius: 16px !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
}

.theme-classic .tag-item:hover {
  background: #e2e8f0 !important;
  border-color: #94a3b8 !important;
}

.theme-classic .tag-item.selected {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

.theme-classic .tag-name {
  color: inherit !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.theme-classic .tag-count {
  background: rgba(0, 0, 0, 0.1) !important;
  color: inherit !important;
  border-radius: 8px !important;
  padding: 1px 4px !important;
  font-size: 10px !important;
  font-weight: 500 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.theme-classic .tag-item.selected .tag-count {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

.theme-classic .show-more-tags {
  background: #f8fafc !important;
  border: 1px dashed #cbd5e1 !important;
  color: #64748b !important;
  opacity: 1 !important;
  visibility: visible !important;
  border-radius: 16px !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  transition: all 0.2s ease !important;
}

.theme-classic .show-more-tags:hover {
  background: #f1f5f9 !important;
  border-color: #94a3b8 !important;
  color: #475569 !important;
}

/* CLASSIC THEME PLAYLISTS */
.theme-classic .empty-playlists-message {
  background: #f8fafc !important;
  border: 1px dashed #cbd5e1 !important;
  color: #64748b !important;
  opacity: 1 !important;
  visibility: visible !important;
  border-radius: 6px !important;
  padding: 16px !important;
  text-align: center !important;
  font-size: 14px !important;
}

.theme-classic .empty-playlists-message p {
  color: #64748b !important;
  margin: 0 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.theme-classic .collection-indicator {
  opacity: 1 !important;
  visibility: visible !important;
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* CLASSIC THEME - Universal visibility fix */
.theme-classic .sidebar,
.theme-classic .sidebar *,
.theme-classic .sidebar *::before,
.theme-classic .sidebar *::after {
  opacity: 1 !important;
  visibility: visible !important;
}

/* CLASSIC THEME - Ensure proper text rendering */
.theme-classic .sidebar span,
.theme-classic .sidebar p,
.theme-classic .sidebar h1,
.theme-classic .sidebar h2,
.theme-classic .sidebar h3,
.theme-classic .sidebar h4,
.theme-classic .sidebar h5,
.theme-classic .sidebar h6 {
  color: inherit !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* CLASSIC THEME - Icon visibility */
.theme-classic .sidebar svg {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
}

/* ========================================
   UNIFIED RIGHT PANELS STYLING
   ======================================== */

/* All Right Panel Containers - Glass Morphism with High Specificity */
.import-panel,
.export-panel,
.organization-panel,
.optimized-panel,
.tabbed-right-panel {
  background: rgba(0, 0, 0, 0.3) !important;
  border-left: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  opacity: 1;
  visibility: visible;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.05) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Glass Morphism - Light Themes with High Specificity */
[data-theme*="light"] .import-panel,
[data-theme*="light"] .export-panel,
[data-theme*="light"] .organization-panel,
[data-theme*="light"] .optimized-panel,
[data-theme*="light"] .tabbed-right-panel {
  background: rgba(255, 255, 255, 0.95) !important;
  border-left: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

/* Panel Headers */
.import-header,
.export-header,
.organization-header,
.panel-header {
  background: var(--tertiary-bg);
  border-bottom: 2px solid var(--border-color);
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

/* Panel Titles */
.import-title,
.export-title,
.organization-title,
.panel-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 18px;
  opacity: 1;
  visibility: visible;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Close Buttons */
.close-btn {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
  border-radius: 6px;
  padding: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.close-btn:hover {
  background: var(--secondary-bg);
  border-color: var(--border-hover);
  color: var(--error-color);
}

/* Panel Content - Glass Morphism */
.import-content,
.export-content,
.organization-content,
.panel-content {
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
  opacity: 1;
  visibility: visible;
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

/* Panel Content - Light Themes */
[data-theme*="light"] .import-content,
[data-theme*="light"] .export-content,
[data-theme*="light"] .organization-content,
[data-theme*="light"] .panel-content {
  color: rgba(0, 0, 0, 0.9);
}

/* Panel Sections - Glass Morphism with High Specificity */
.import-section,
.export-section,
.organization-section {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  opacity: 1;
  visibility: visible;
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Panel Sections - Light Themes with High Specificity */
[data-theme*="light"] .import-section,
[data-theme*="light"] .export-section,
[data-theme*="light"] .organization-section {
  background: rgba(0, 0, 0, 0.05) !important;
  color: rgba(0, 0, 0, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Section Titles */
.section-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 16px;
  opacity: 1;
  visibility: visible;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Section Descriptions - Glass Morphism */
.section-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  opacity: 1;
  visibility: visible;
  margin-bottom: 16px;
  line-height: 1.5;
}

/* Section Descriptions - Light Themes */
[data-theme*="light"] .section-description {
  color: rgba(0, 0, 0, 0.7);
}

/* Format Options */
.format-options {
  opacity: 1;
  visibility: visible;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.format-option {
  background: var(--tertiary-bg);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
}

.format-option:hover {
  background: var(--secondary-bg);
  border-color: var(--border-hover);
}

.format-option.active,
.format-option.primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

.format-option.active:hover,
.format-option.primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

/* ========================================
   UNIFIED TABBED INTERFACE SYSTEM
   ======================================== */

/* Tabbed Panel Container - Already styled above with other panels */

/* Tab Header */
.tab-header {
  background: var(--tertiary-bg);
  border-bottom: 2px solid var(--border-color);
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  flex-shrink: 0;
}

/* Tab List */
.tab-list {
  display: flex;
  flex: 1;
  overflow-x: auto;
  gap: 4px;
  opacity: 1;
  visibility: visible;
}

/* Tab Buttons */
.tab-button {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
  border-radius: 6px 6px 0 0;
  padding: 8px 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
  min-width: 0;
  position: relative;
}

.tab-button:hover {
  background: var(--tertiary-bg);
  border-color: var(--border-hover);
}

.tab-button.active {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
  border-bottom: 3px solid var(--accent-color);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* Tab Labels */
.tab-label {
  color: inherit;
  font-size: 14px;
  font-weight: 500;
  opacity: 1;
  visibility: visible;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Tab Close Buttons */
.tab-close {
  background: transparent;
  border: none;
  color: inherit;
  opacity: 0.7;
  visibility: visible;
  border-radius: 4px;
  padding: 2px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.tab-close:hover {
  background: rgba(0, 0, 0, 0.1);
  opacity: 1;
}

.tab-button.active .tab-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Close All Button */
.close-all-btn {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
  border-radius: 6px;
  padding: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  margin-left: 8px;
}

.close-all-btn:hover {
  background: var(--secondary-bg);
  border-color: var(--border-hover);
  color: var(--error-color);
}

/* Tab Content Area - Already styled above with panel-content */

/* ========================================
   UNIFIED FORM ELEMENTS
   ======================================== */

/* Input Fields */
.input-compact,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
  background: var(--tertiary-bg);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  font-size: 14px;
}

.input-compact:focus,
input:focus,
textarea:focus,
select:focus {
  border-color: var(--accent-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

/* Drop Zones with Glass Morphism */
.drop-zone-compact,
.drop-zone {
  background: rgba(0, 0, 0, 0.2) !important;
  border: 2px dashed rgba(255, 255, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  opacity: 1;
  visibility: visible;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

/* Drop Zones - Light Themes */
[data-theme*="light"] .drop-zone-compact,
[data-theme*="light"] .drop-zone {
  background: rgba(255, 255, 255, 0.6) !important;
  border: 2px dashed rgba(0, 0, 0, 0.2) !important;
  color: rgba(0, 0, 0, 0.8) !important;
}

.drop-zone-compact:hover,
.drop-zone:hover,
.drop-zone-compact.active,
.drop-zone.active {
  background: rgba(0, 0, 0, 0.3) !important;
  border-color: #4A9EFF !important;
  color: rgba(255, 255, 255, 0.95) !important;
}

/* Drop Zones Hover - Light Themes */
[data-theme*="light"] .drop-zone-compact:hover,
[data-theme*="light"] .drop-zone:hover,
[data-theme*="light"] .drop-zone-compact.active,
[data-theme*="light"] .drop-zone.active {
  background: rgba(255, 255, 255, 0.8) !important;
  color: rgba(0, 0, 0, 0.95) !important;
}

/* Progress Bars */
.progress-compact,
.progress-bar-container {
  background: var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  height: 8px;
  opacity: 1;
  visibility: visible;
}

.progress-bar {
  background: var(--accent-color);
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 8px;
}

/* Buttons */
.btn-compact,
.multimedia-btn,
button:not(.close-btn):not(.tab-close):not(.tab-button):not(.close-all-btn) {
  background: var(--tertiary-bg);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  opacity: 1;
  visibility: visible;
  border-radius: 6px;
  padding: 8px 16px;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-compact:hover,
.multimedia-btn:hover,
button:not(.close-btn):not(.tab-close):not(.tab-button):not(.close-all-btn):hover {
  background: var(--secondary-bg);
  border-color: var(--border-hover);
}

.btn-compact.primary,
.multimedia-btn--primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

.btn-compact.primary:hover,
.multimedia-btn--primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

/* ========================================
   UNIFIED MOBILE RESPONSIVENESS
   ======================================== */

/* Mobile Breakpoints for Right Panels */
@media (max-width: 768px) {

  .import-panel,
  .export-panel,
  .organization-panel,
  .optimized-panel,
  .tabbed-right-panel {
    width: 100vw !important;
    height: 100vh !important;
    top: 0 !important;
    right: 0 !important;
    left: 0 !important;
    border-left: none !important;
    border-top: 3px solid var(--border-color) !important;
    z-index: 9999 !important;
  }

  .tab-header {
    padding: 12px 16px !important;
    flex-wrap: wrap !important;
  }

  .tab-list {
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  .tab-list::-webkit-scrollbar {
    display: none !important;
  }

  .tab-button {
    min-width: 120px !important;
    flex-shrink: 0 !important;
  }

  .import-content,
  .export-content,
  .organization-content,
  .panel-content {
    padding: 16px !important;
  }

  .import-section,
  .export-section,
  .organization-section {
    padding: 12px !important;
    margin-bottom: 16px !important;
  }

  .format-option {
    padding: 12px !important;
  }

  .section-title {
    font-size: 14px !important;
  }

  .section-description {
    font-size: 13px !important;
  }
}

/* Tablet Breakpoints */
@media (max-width: 1024px) and (min-width: 769px) {

  .import-panel,
  .export-panel,
  .organization-panel,
  .optimized-panel,
  .tabbed-right-panel {
    width: 350px !important;
  }
}

/* Large Screen Optimization */
@media (min-width: 1440px) {

  .import-panel,
  .export-panel,
  .organization-panel,
  .optimized-panel,
  .tabbed-right-panel {
    width: 450px !important;
  }
}

/* ========================================
   UNIFIED UNIVERSAL VISIBILITY
   ======================================== */

/* Ensure all panel content is visible */
.import-panel *,
.export-panel *,
.organization-panel *,
.optimized-panel *,
.tabbed-right-panel *,
.sidebar * {
  opacity: 1;
  visibility: visible;
}

/* Ensure proper text rendering */
.import-panel span,
.import-panel p,
.import-panel h1,
.import-panel h2,
.import-panel h3,
.import-panel h4,
.import-panel h5,
.import-panel h6,
.export-panel span,
.export-panel p,
.export-panel h1,
.export-panel h2,
.export-panel h3,
.export-panel h4,
.export-panel h5,
.export-panel h6,
.organization-panel span,
.organization-panel p,
.organization-panel h1,
.organization-panel h2,
.organization-panel h3,
.organization-panel h4,
.organization-panel h5,
.organization-panel h6,
.sidebar span,
.sidebar p,
.sidebar h1,
.sidebar h2,
.sidebar h3,
.sidebar h4,
.sidebar h5,
.sidebar h6 {
  color: inherit;
  opacity: 1;
  visibility: visible;
}

/* Icon visibility */
.import-panel svg,
.export-panel svg,
.organization-panel svg,
.tabbed-right-panel svg,
.sidebar svg {
  opacity: 1;
  visibility: visible;
  color: inherit;
}

/* ========================================
   UNIFIED HEADER STYLING
   ======================================== */

/* Header Container with Glass Morphism */
.header {
  background: rgba(0, 0, 0, 0.3) !important;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05) !important;
}

/* Header - Light Themes */
[data-theme*="light"] .header {
  background: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

/* Header Sections */
.header-left,
.header-center,
.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.header-left {
  flex: 0 0 auto;
}

.header-center {
  flex: 1 1 auto;
  justify-content: center;
  min-width: 200px;
}

.header-right {
  flex: 0 0 auto;
}

/* Header Search */
.header-search {
  flex: 1;
  max-width: 400px;
  min-width: 200px;
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  gap: 4px;
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 4px;
}

.filter-button {
  background: transparent;
  border: none;
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.filter-button:hover {
  background: var(--secondary-bg);
}

.filter-button.active {
  background: var(--accent-color);
  color: #ffffff;
}

/* Header Buttons */
.header-button {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.header-button:hover {
  background: var(--secondary-bg);
  border-color: var(--border-hover);
}

.header-button.primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

.header-button.primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

/* Auto Save Indicator */
.auto-save-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 12px;
}

.auto-save-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.auto-save-toggle {
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.auto-save-toggle:hover {
  background: var(--secondary-bg);
}

/* Mobile Responsiveness for Header */
@media (max-width: 768px) {
  .header {
    padding: 8px 12px;
    flex-direction: column;
    gap: 8px;
    min-height: auto;
  }

  .header-left,
  .header-center,
  .header-right {
    width: 100%;
    justify-content: center;
  }

  .header-center {
    order: 1;
  }

  .header-left {
    order: 2;
  }

  .header-right {
    order: 3;
  }

  .header-search {
    max-width: none;
    min-width: auto;
  }

  .filter-buttons {
    width: 100%;
    justify-content: center;
  }

  .auto-save-indicator {
    font-size: 11px;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {

  .header-left,
  .header-center,
  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .filter-buttons {
    flex-direction: column;
    width: 100%;
  }

  .filter-button {
    width: 100%;
    text-align: center;
  }
}

/* Button active states for click feedback */
.btn-primary.active,
.btn-secondary.active {
  transform: scale(0.98);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  background-color: var(--accent-hover);
  border-color: var(--accent-hover);
}

.format-option span {
  font-weight: 500;
}

.format-option small {
  display: block;
  opacity: 0.8;
  font-size: 12px;
  margin-top: 2px;
}

.import-options {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.checkbox-option {
  display: flex;
  align-items: flex-start;
  gap: var(--padding-md);
  cursor: pointer;
  padding: var(--padding-md);
  background-color: var(--tertiary-bg);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  border: 2px solid transparent;
}

.checkbox-option:hover {
  background-color: var(--border-color);
}

.checkbox-option:has(input:checked) {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: var(--accent-color);
}

.checkbox-option input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--accent-color);
  cursor: pointer;
  margin: 0;
  flex-shrink: 0;
}

.checkbox-label {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
  cursor: pointer;
}

/* Auto-Organize Panel Styles */
.strategy-description {
  background-color: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--padding-md);
  margin-top: var(--padding-sm);
}

.strategy-description p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

.option-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-sm);
  background-color: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.option-item:hover {
  background-color: var(--border-hover);
  border-color: var(--accent-color);
}

.option-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--accent-color);
  cursor: pointer;
  margin: 0;
}

.option-item span {
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
}

.status-message {
  display: flex;
  align-items: flex-start;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  border-radius: var(--radius-md);
  border: 1px solid;
}

.status-message.success {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: var(--success-color);
}

.status-message.error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: var(--error-color);
}

.status-message h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.status-message p {
  margin: 0 0 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.stats {
  display: flex;
  gap: var(--padding-md);
  font-size: 12px;
  opacity: 0.8;
}

.preview-summary {
  background-color: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--padding-md);
  margin-bottom: var(--padding-md);
}

.preview-summary p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

.preview-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--padding-md);
}

.preview-group h4 {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 var(--padding-sm) 0;
}

.preview-list {
  max-height: 120px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.preview-item {
  padding: 6px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  border: 1px solid;
}

.preview-item.success {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: var(--success-color);
}

.preview-item.info {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: var(--accent-color);
}

.preview-item.more {
  background-color: var(--tertiary-bg);
  border-color: var(--border-color);
  color: var(--text-secondary);
  font-style: italic;
}

.ready-status {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: var(--radius-md);
  color: var(--accent-color);
}

.ready-status h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.ready-status p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* UNIFIED GLASS MORPHISM TABBED PANEL with High Specificity */
.tabbed-right-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.3) !important;
  border-left: 2px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
}

/* UNIFIED GLASS MORPHISM TABBED PANEL - LIGHT THEMES with High Specificity */
[data-theme*="light"] .tabbed-right-panel {
  background: rgba(255, 255, 255, 0.95) !important;
  border-left: 2px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1) !important;
}

/* UNIFIED GLASS MORPHISM TAB HEADER */
.tab-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  min-height: 48px;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* UNIFIED GLASS MORPHISM TAB HEADER - LIGHT THEMES */
[data-theme*="light"] .tab-header {
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.tab-list {
  display: flex;
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-list::-webkit-scrollbar {
  display: none;
}

/* UNIFIED GLASS MORPHISM TAB ITEMS */
.tab-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 0;
  position: relative;
  background: rgba(0, 0, 0, 0.1);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.15);
  border-bottom: 3px solid #4A9EFF;
  box-shadow: 0 -2px 8px rgba(74, 158, 255, 0.3);
}

/* Tab Items - Light Themes */
[data-theme*="light"] .tab-item {
  background: rgba(255, 255, 255, 0.3);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme*="light"] .tab-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

[data-theme*="light"] .tab-item.active {
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 3px solid #4A9EFF;
  box-shadow: 0 -2px 8px rgba(74, 158, 255, 0.2);
}

/* Tab item text styling */

.tab-item span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Light theme tab text adjustments */
[data-theme*="light"] .tab-item span {
  color: rgba(0, 0, 0, 0.8);
}

.tab-item.active span {
  color: var(--accent-color);
}

.tab-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: var(--radius-sm);
  background-color: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-left: var(--padding-xs);
}

.tab-close:hover {
  background-color: var(--error-color);
  color: white;
}

.close-all-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-left: 1px solid var(--border-color);
}

.close-all-btn:hover {
  background-color: var(--error-color);
  color: white;
}

.tab-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.tab-content .import-panel,
.tab-content .export-panel,
.tab-content .split-panel,
.tab-content .playlist-panel,
.tab-content .organization-panel {
  position: static;
  width: 100%;
  height: 100%;
  border: none;
  box-shadow: none;
  /* PRESERVE GLASS MORPHISM - Don't override background */
  display: flex;
  flex-direction: column;
}

.tab-content .import-panel .import-header,
.tab-content .export-panel .export-header,
.tab-content .split-panel .split-header,
.tab-content .playlist-panel .playlist-header,
.tab-content .organization-panel .import-header {
  display: none;
}

.tab-content .import-content,
.tab-content .export-content,
.tab-content .split-content,
.tab-content .playlist-content,
.tab-content .organization-content {
  flex: 1;
  height: calc(100vh - 48px);
  overflow-y: auto;
  padding: var(--padding-md);
  /* PRESERVE GLASS MORPHISM COLORS */
  color: rgba(255, 255, 255, 0.9);
}

/* Tab Content - Light Themes */
[data-theme*="light"] .tab-content .import-content,
[data-theme*="light"] .tab-content .export-content,
[data-theme*="light"] .tab-content .split-content,
[data-theme*="light"] .tab-content .playlist-content,
[data-theme*="light"] .tab-content .organization-content {
  color: rgba(0, 0, 0, 0.9);
}

/* Ensure panels work properly in tabbed mode with glass morphism - High Specificity */
.tab-content .import-panel,
.tab-content .export-panel,
.tab-content .split-panel,
.tab-content .playlist-panel,
.tab-content .organization-panel {
  height: 100%;
  /* PRESERVE GLASS MORPHISM BACKGROUND with High Specificity */
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
}

/* Tab Content Panels - Light Themes with High Specificity */
[data-theme*="light"] .tab-content .import-panel,
[data-theme*="light"] .tab-content .export-panel,
[data-theme*="light"] .tab-content .split-panel,
[data-theme*="light"] .tab-content .playlist-panel,
[data-theme*="light"] .tab-content .organization-panel {
  background: rgba(255, 255, 255, 0.95) !important;
}

/* Fix for split panel specific elements */
.tab-content .split-panel .split-actions,
.tab-content .playlist-panel .playlist-actions {
  margin-top: auto;
  padding-top: var(--padding-md);
  border-top: 1px solid var(--border-color);
}

/* Panel optimization removed - restored original functionality */

/* Optimized panel styling removed - restored original panel functionality */

/* Specific panel optimizations removed - restored original functionality */

/* Responsive and animation optimizations removed - restored original functionality */

/* Playlist Panel Specific Styles */
.playlist-section {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.playlist-section-title {
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.playlist-section-description {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: var(--spacing-md);
}

.playlist-input-group {
  margin-bottom: var(--spacing-md);
}

.playlist-label {
  display: block;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
  font-size: 14px;
}

.playlist-input-field {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--primary-bg);
  color: var(--text-primary);
  font-size: 14px;
  transition: all var(--transition-fast);
}

.playlist-input-field:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

.playlist-textarea-field {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--primary-bg);
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
  transition: all var(--transition-fast);
  resize: vertical;
  min-height: 80px;
}

.playlist-textarea-field:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

/* UNIFIED SMART PLAYLIST STYLES - ALWAYS MODERN */
.playlist-panel .smart-controls {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.playlist-panel .smart-btn {
  padding: 8px 12px;
  /* Always use modern glass design */
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

/* Light theme smart button adjustments */
.light .playlist-panel .smart-btn {
  background: rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.8);
}

.playlist-panel .smart-btn:hover:not(:disabled) {
  border-color: #4A9EFF;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.playlist-panel .smart-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Light theme smart button hover */
.light .playlist-panel .smart-btn:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.05);
  border-color: #4A9EFF;
}

/* Specific Smart Button Variants */
.playlist-panel .generate-btn {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  color: var(--primary-bg);
  border-color: var(--accent-color);
}

.playlist-panel .generate-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--accent-hover), var(--accent-color));
  box-shadow: var(--shadow-md);
}

.playlist-panel .auto-create-btn {
  background: linear-gradient(135deg, var(--success-color), #16a34a);
  color: var(--primary-bg);
  border-color: var(--success-color);
}

.playlist-panel .auto-create-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, var(--success-color));
  box-shadow: var(--shadow-md);
}

/* Smart Suggestions Panel */
.playlist-panel .smart-suggestions-panel {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.playlist-panel .suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.playlist-panel .suggestions-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.playlist-panel .close-suggestions-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.playlist-panel .close-suggestions-btn:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.generate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.auto-create-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.auto-create-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 87, 108, 0.4);
}

.smart-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.smart-suggestions-panel {
  background: var(--panel-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.suggestion-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.suggestion-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-header {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.suggestion-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.suggestion-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-muted);
  margin-top: 4px;
}

.create-from-suggestion-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-from-suggestion-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.4);
}

.analytics-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.analytics-modal {
  background: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Analytics Modal - Light Themes */
[data-theme*="light"] .analytics-modal {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.analytics-content {
  padding: 20px;
  overflow-y: auto;
}

.analytics-section {
  margin-bottom: 20px;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.analytics-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.analytics-label {
  display: block;
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: 4px;
}

.analytics-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.category-item,
.domain-item,
.pattern-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 4px;
  background: var(--secondary-bg);
  border-radius: 4px;
}

.category-bar {
  flex: 1;
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  margin: 0 8px;
  overflow: hidden;
}

.category-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.recommendation-item {
  background: var(--secondary-bg);
  border-left: 3px solid var(--accent-color);
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 0 4px 4px 0;
}

/* Export Panel Specific Styles */
.filename-input {
  margin-bottom: var(--padding-lg);
}

.filename-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: var(--padding-sm);
}

.filename-field {
  flex: 1;
  padding: var(--padding-md);
  padding-right: 60px;
  /* Space for extension */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: 14px;
  transition: border-color var(--transition-fast);
}

.filename-field:focus {
  outline: none;
  border-color: var(--accent-color);
}

.filename-field::placeholder {
  color: var(--text-muted);
}

.file-extension {
  position: absolute;
  right: 12px;
  color: var(--text-muted);
  font-size: 14px;
  pointer-events: none;
}

.export-source {
  margin-bottom: var(--padding-lg);
}

.export-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--padding-xl);
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--secondary-bg);
  text-align: center;
  min-height: 200px;
  transition: all var(--transition-fast);
}

.upload-hint.warning {
  color: var(--warning-color);
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  margin-top: var(--padding-sm);
  font-size: 13px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tabbed-right-panel {
    width: 100vw;
    left: 0;
  }

  .tab-item span {
    display: none;
  }

  .tab-item {
    min-width: 48px;
    justify-content: center;
  }
}

/* Drag & Drop Zone Styles with Enhanced Glass Morphism */
.drag-drop-zone {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  visibility: visible;
  transition: all var(--transition-normal);
}

/* Drag Drop Zone - Light Themes */
[data-theme*="light"] .drag-drop-zone {
  background: rgba(255, 255, 255, 0.8) !important;
}

.drag-drop-zone.drag-over {
  opacity: 1;
  visibility: visible;
  background-color: rgba(59, 130, 246, 0.2);
}

.drag-drop-content {
  background: var(--primary-bg);
  border: 2px dashed var(--accent-color);
  border-radius: var(--radius-xl);
  padding: var(--padding-xl);
  text-align: center;
  max-width: 400px;
  box-shadow: var(--shadow-lg);
}

.drag-drop-zone.processing .drag-drop-content {
  border-style: solid;
  background: var(--secondary-bg);
}

.drag-drop-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: var(--padding-md) 0 var(--padding-sm);
}

.drag-drop-hint {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.drag-drop-zone .processing-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.drag-drop-zone .success-icon {
  color: var(--success-color);
}

/* Removed Recently Added Indicator Styles - using left panel filter instead */

/* Theme Toggle Styles */
.theme-toggle-container {
  position: relative;
  z-index: 1000;
}

.theme-toggle-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 10px;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
  z-index: 1001;
}

.theme-toggle-btn:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.theme-toggle-btn .chevron {
  transition: transform var(--transition-fast);
}

.theme-toggle-btn .chevron.open {
  transform: rotate(180deg);
}

.theme-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.theme-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(16px);
  min-width: 380px;
  max-width: 450px;
  max-height: 70vh;
  z-index: 1000;
  animation: slideDown 0.2s ease-out;
  overflow-y: auto;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.theme-dropdown-header {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
}

.theme-sections {
  padding: var(--padding-md);
}

.theme-section {
  margin-bottom: var(--padding-lg);
}

.theme-section:last-child {
  margin-bottom: 0;
}

.theme-section-title {
  margin: 0 0 var(--padding-sm) 0;
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--padding-sm);
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--padding-xs);
  padding: var(--padding-sm);
  background: var(--tertiary-bg);
  border: 2px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 80px;
}

.theme-option:hover {
  background: var(--primary-bg);
  border-color: var(--border-hover);
}

.theme-option.active {
  border-color: var(--accent-color);
  background: var(--primary-bg);
}

.theme-preview {
  display: flex;
  gap: 2px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  width: 48px;
  height: 24px;
}

.theme-color {
  flex: 1;
  height: 100%;
}

.theme-color.primary {
  flex: 2;
}

.theme-name {
  font-size: 11px;
  font-weight: 500;
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.2;
}

.theme-option.active .theme-name {
  color: var(--accent-color);
  font-weight: 600;
}

.theme-dropdown-footer {
  padding: var(--padding-md);
  border-top: 1px solid var(--border-color);
  margin-top: 0;
}

.custom-theme-btn {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  width: 100%;
  padding: var(--padding-sm);
  background: var(--accent-color);
  border: none;
  color: var(--text-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 13px;
  font-weight: 500;
  justify-content: center;
}

.custom-theme-btn:hover {
  background: var(--accent-hover);
}

/* Custom Theme Editor */
.custom-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 2000;
}

.custom-theme-editor {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.4);
  width: 90vw;
  max-width: 600px;
  max-height: 80vh;
  z-index: 2001;
  display: flex;
  flex-direction: column;
}

.custom-editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--padding-lg);
  border-bottom: 1px solid var(--border-color);
}

.custom-editor-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 24px;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.close-btn:hover {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

.custom-editor-content {
  flex: 1;
  padding: var(--padding-lg);
  overflow-y: auto;
}

.theme-name-input {
  margin-bottom: var(--padding-lg);
}

.theme-name-input label {
  display: block;
  margin-bottom: var(--padding-xs);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.theme-name-input input {
  width: 100%;
  padding: var(--padding-sm);
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 14px;
}

.theme-name-input input:focus {
  outline: none;
  border-color: var(--accent-color);
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--padding-md);
}

.color-field {
  display: flex;
  flex-direction: column;
  gap: var(--padding-xs);
}

.color-field label {
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
}

.color-input-group {
  display: flex;
  gap: var(--padding-xs);
  align-items: center;
}

.color-picker {
  width: 40px;
  height: 32px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  cursor: pointer;
  background: none;
}

.color-text {
  flex: 1;
  padding: var(--padding-xs);
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: 12px;
  font-family: monospace;
}

.color-text:focus {
  outline: none;
  border-color: var(--accent-color);
}

.custom-editor-footer {
  display: flex;
  gap: var(--padding-sm);
  padding: var(--padding-lg);
  border-top: 1px solid var(--border-color);
  justify-content: flex-end;
}

.cancel-btn {
  padding: var(--padding-sm) var(--padding-md);
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
}

.cancel-btn:hover {
  background: var(--primary-bg);
  color: var(--text-primary);
}

.save-btn {
  padding: var(--padding-sm) var(--padding-md);
  background: var(--accent-color);
  border: none;
  color: var(--text-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
}

.save-btn:hover {
  background: var(--accent-hover);
}

/* Tree View Styles */
.tree-view {
  padding: var(--padding-lg);
  height: 100%;
  overflow-y: auto;
}

.tree-header {
  margin-bottom: var(--padding-lg);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--padding-md);
}

.tree-title {
  margin: 0 0 var(--padding-xs) 0;
  color: var(--text-primary);
  font-size: 24px;
  font-weight: 600;
}

.tree-subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.tree-container {
  position: relative;
}

.tree-node {
  position: relative;
}

.tree-node-content {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  padding: var(--padding-xs) var(--padding-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 32px;
  position: relative;
}

.tree-node-content:hover {
  background-color: var(--tertiary-bg);
}

.tree-node-content.drag-over {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.tree-node-content.dragging {
  opacity: 0.5;
}

.tree-expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.tree-expand-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.tree-node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  color: var(--text-secondary);
}

.tree-favicon {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
}

.tree-fallback-icon {
  color: var(--text-muted);
}

.tree-node-label {
  flex: 1;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.tree-node-count {
  color: var(--text-muted);
  font-size: 12px;
  font-weight: 400;
  margin-left: var(--padding-xs);
}

.tree-node-actions {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.tree-node-content:hover .tree-node-actions {
  opacity: 1;
}

.tree-favorite-icon {
  color: var(--warning-color);
  fill: currentColor;
}

.tree-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.tree-action-btn:hover {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.tree-children {
  position: relative;
}

.tree-children::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: var(--border-color);
  opacity: 0.5;
}

/* Modern Tree View Styles - Mobile-Friendly */
.tree-view-modern {
  padding: var(--padding-lg);
  height: 100%;
  overflow-y: auto;
  max-width: 100%;
}

.tree-container-modern {
  position: relative;
  width: 100%;
}

.tree-node-modern {
  position: relative;
  width: 100%;
}

.tree-node-content-modern {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md) var(--padding-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 48px;
  /* Larger touch targets for mobile */
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.tree-node-content-modern:hover {
  background-color: var(--tertiary-bg);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tree-node-content-modern.tree-drag-over {
  background-color: var(--accent-color);
  color: var(--text-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tree-node-content-modern.tree-dragging {
  opacity: 0.6;
  transform: scale(0.98);
}

.tree-expand-btn-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.tree-expand-btn-modern:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
  transform: scale(1.1);
}

.tree-node-icon-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  color: var(--text-secondary);
}

.tree-favicon-modern {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-sm);
  object-fit: cover;
}

.tree-fallback-icon-modern {
  color: var(--text-muted);
}

.tree-node-label-modern {
  flex: 1;
  color: var(--text-primary);
  font-size: 16px;
  /* Larger text for mobile */
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  line-height: 1.4;
}

.tree-node-count-modern {
  color: var(--text-muted);
  font-size: 14px;
  font-weight: 400;
  margin-left: var(--padding-sm);
  background-color: var(--secondary-bg);
  padding: 2px 8px;
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
}

.tree-node-actions-modern {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.tree-node-content-modern:hover .tree-node-actions-modern {
  opacity: 1;
}

.tree-favorite-icon-modern {
  color: var(--warning-color);
  fill: currentColor;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.tree-action-btn-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.tree-action-btn-modern:hover {
  background-color: var(--accent-color);
  color: var(--text-primary);
  transform: scale(1.1);
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  .tree-view-modern {
    padding: var(--padding-md);
  }

  .tree-node-content-modern {
    min-height: 52px;
    /* Even larger touch targets on mobile */
    padding: var(--padding-md);
  }

  .tree-node-label-modern {
    font-size: 17px;
    /* Slightly larger on mobile */
  }

  .tree-expand-btn-modern,
  .tree-action-btn-modern {
    width: 36px;
    height: 36px;
  }

  .tree-node-icon-modern {
    width: 28px;
    height: 28px;
  }

  .tree-favicon-modern {
    width: 22px;
    height: 22px;
  }
}

/* Modern Tree View - Inspired by VS Code, Notion, and modern file explorers */
.modern-tree-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--primary-bg);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow: hidden;
}

.modern-tree-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--primary-bg);
  flex-shrink: 0;
}

.header-content {
  max-width: 100%;
}

.modern-tree-header .tree-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.modern-tree-header .tree-subtitle {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

.modern-tree-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px 0;
}

.modern-tree-item {
  position: relative;
  width: 100%;
}

.modern-tree-content {
  display: flex;
  align-items: center;
  height: 28px;
  cursor: pointer;
  border-radius: 6px;
  margin: 0 8px;
  transition: all 0.15s ease;
  position: relative;
  user-select: none;
}

.modern-tree-content:hover {
  background-color: var(--tertiary-bg);
}

.modern-tree-content.folder {
  font-weight: 500;
}

.modern-tree-content.bookmark {
  font-weight: 400;
}

.modern-tree-content.drag-over {
  background-color: var(--accent-color);
  color: white;
}

.modern-tree-content.dragging {
  opacity: 0.5;
}

.modern-tree-expand {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.modern-expand-btn {
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.15s ease;
}

.modern-expand-btn:hover {
  background-color: var(--secondary-bg);
  color: var(--text-primary);
}

.expand-icon {
  transition: transform 0.15s ease;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.expand-spacer {
  width: 16px;
  height: 16px;
}

.modern-tree-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 8px;
}

.folder-icon {
  color: #54aeff;
  transition: color 0.15s ease;
}

.folder-icon.open {
  color: #54aeff;
}

.folder-icon.closed {
  color: #8cc8ff;
}

.bookmark-icon {
  position: relative;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-favicon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  object-fit: cover;
}

.favicon-fallback {
  color: var(--text-muted);
  width: 14px;
  height: 14px;
}

.modern-tree-label {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
  gap: 8px;
}

.label-text {
  font-size: 13px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.item-count {
  font-size: 11px;
  color: var(--text-muted);
  background-color: var(--secondary-bg);
  padding: 1px 6px;
  border-radius: 10px;
  font-weight: 500;
  min-width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.modern-tree-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.15s ease;
  margin-left: 8px;
}

.modern-tree-content:hover .modern-tree-actions {
  opacity: 1;
}

.favorite-indicator {
  color: #fbbf24;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.15s ease;
}

.action-btn:hover {
  background-color: var(--secondary-bg);
  color: var(--text-primary);
}

.modern-tree-children {
  position: relative;
}

/* Subtle connection lines */
.modern-tree-children::before {
  content: '';
  position: absolute;
  left: 18px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to bottom, var(--border-color) 0%, transparent 100%);
  opacity: 0.3;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .modern-tree-header {
    padding: 16px 20px 12px;
  }

  .modern-tree-content {
    height: 36px;
    margin: 0 12px;
  }

  .label-text {
    font-size: 14px;
  }

  .item-count {
    font-size: 12px;
    padding: 2px 7px;
  }

  .modern-tree-actions {
    margin-left: 12px;
  }

  .action-btn {
    width: 24px;
    height: 24px;
  }
}

/* TreeView Styles - Consistent with App Design */
.tree-view {
  height: 100%;
  background: var(--primary-bg);
  overflow: hidden;
}

.tree-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--padding-lg);
}

.tree-node {
  width: 100%;
}

.tree-node-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 1px 0;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 32px;
  gap: 8px;
}

.tree-node-content:hover {
  background-color: var(--tertiary-bg);
}

.tree-node-content.folder {
  font-weight: 500;
}

.tree-node-content.bookmark {
  font-weight: 400;
}

.tree-expand {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.tree-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}

.tree-icon svg {
  color: var(--accent-color);
}

.tree-favicon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  object-fit: cover;
}

.tree-favicon-fallback {
  color: var(--text-muted);
}

.tree-label {
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.tree-count {
  color: var(--text-muted);
  font-size: 12px;
  font-weight: 400;
  margin-left: 8px;
}

.tree-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.tree-node-content:hover .tree-actions {
  opacity: 1;
}

.tree-action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.tree-action-btn:hover {
  background-color: var(--secondary-bg);
  color: var(--accent-color);
}

.tree-children {
  position: relative;
}

/* Connection lines for better hierarchy visualization */
.tree-children::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: var(--border-color);
  opacity: 0.3;
}

/* Mobile optimizations for TreeView */
@media (max-width: 768px) {
  .tree-container {
    padding: var(--padding-md);
  }

  .tree-node-content {
    padding: 10px 12px;
    min-height: 40px;
  }

  .tree-label {
    font-size: 15px;
  }

  .tree-action-btn {
    width: 28px;
    height: 28px;
  }
}

/* ListView Selection Styles */
.list-col-select {
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
}

.selection-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid var(--text-secondary);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.selection-checkbox.checked {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.checkbox-mark {
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 1px;
}

.list-row.selected {
  background-color: var(--accent-color);
  color: white;
}

.list-row.selected .list-col-title,
.list-row.selected .list-col-url,
.list-row.selected .list-col-collection,
.list-row.selected .list-col-date {
  color: white;
}

.list-row.selected .list-col-url {
  color: rgba(255, 255, 255, 0.8);
}

/* View Toggle Styles */
.view-toggle {
  display: flex;
  gap: 2px;
  background: var(--tertiary-bg);
  padding: 2px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  padding: 6px 12px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

.view-toggle-btn:hover {
  background-color: var(--secondary-bg);
  color: var(--text-primary);
}

.view-toggle-btn.active {
  background-color: var(--accent-color);
  color: var(--text-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-toggle-label {
  font-size: 12px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .view-toggle-label {
    display: none;
  }

  .view-toggle-btn {
    padding: 6px 8px;
  }
}

/* List View Styles */
.list-view {
  padding: var(--padding-lg);
  height: 100%;
  overflow-y: auto;
}

.list-header {
  margin-bottom: var(--padding-lg);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--padding-md);
}

.list-title {
  margin: 0 0 var(--padding-xs) 0;
  color: var(--text-primary);
  font-size: 24px;
  font-weight: 600;
}

.list-subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.list-container {
  position: relative;
}

.list-table {
  width: 100%;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.list-table-header {
  display: grid;
  grid-template-columns: 40px 1fr 200px 120px 100px 80px;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  background-color: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-color);
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.list-table-body {
  background-color: var(--secondary-bg);
}

.list-row {
  display: grid;
  grid-template-columns: 40px 1fr 200px 120px 100px 80px;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.list-row:hover {
  background-color: var(--tertiary-bg);
}

.list-row:last-child {
  border-bottom: none;
}

.list-col-favicon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-favicon {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
}

.list-fallback-icon {
  color: var(--text-muted);
}

.list-col-title {
  display: flex;
  align-items: center;
  min-width: 0;
}

.list-title-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 0;
}

.list-bookmark-title {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-bookmark-description {
  color: var(--text-muted);
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-tags {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 2px;
}

.list-tag {
  background-color: var(--tertiary-bg);
  color: var(--text-secondary);
  font-size: 10px;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  white-space: nowrap;
}

.list-tag-more {
  color: var(--text-muted);
  font-size: 10px;
}

.list-col-url {
  display: flex;
  align-items: center;
  min-width: 0;
}

.list-url {
  color: var(--text-secondary);
  font-size: 12px;
  font-family: monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-col-collection {
  display: flex;
  align-items: center;
}

.list-collection {
  color: var(--text-secondary);
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-col-date {
  display: flex;
  align-items: center;
}

.list-date {
  color: var(--text-muted);
  font-size: 12px;
  white-space: nowrap;
}

.list-col-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.list-row:hover .list-col-actions {
  opacity: 1;
}

.list-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.list-action-btn:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.list-action-btn.favorite.active {
  color: var(--warning-color);
}

.list-action-btn.external:hover {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

.list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--padding-xl);
  text-align: center;
}

.list-empty-icon {
  color: var(--text-muted);
  margin-bottom: var(--padding-md);
}

.list-empty-title {
  margin: 0 0 var(--padding-sm) 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.list-empty-description {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

@media (max-width: 1024px) {

  .list-table-header,
  .list-row {
    grid-template-columns: 40px 1fr 150px 100px 60px;
  }

  .list-col-date {
    display: none;
  }
}

@media (max-width: 768px) {

  .list-table-header,
  .list-row {
    grid-template-columns: 40px 1fr 60px;
  }

  .list-col-url,
  .list-col-collection {
    display: none;
  }
}

.revert-btn {
  background: none;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  padding: 8px;
}

.revert-btn:hover {
  background-color: var(--error-color);
  border-color: var(--error-color);
  color: var(--text-primary);
}

.bookmark-card {
  position: relative;
}

.bookmark-card.recently-added {
  border: 2px solid var(--success-color);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
  animation: slideInFromTop 0.5s ease-out;
}

.bookmark-card.recently-added.saving {
  animation: fadeOutNewStatus 0.5s ease-out forwards;
}

@keyframes fadeOutNewStatus {
  0% {
    border-color: var(--success-color);
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
  }

  100% {
    border-color: var(--border-color);
    box-shadow: none;
  }
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-30px) scale(0.9);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }

  50% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
  }
}

/* Enhanced drag zone animations */
.drag-drop-zone {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }

  100% {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

.drag-drop-content {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Improved hover effects for revert button */
.revert-btn {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.revert-btn:hover {
  transform: scale(1.1) rotate(-5deg);
}

.revert-btn:active {
  transform: scale(0.95) rotate(-5deg);
}

/* Bookmark Health Checker - Exact Import Panel Styling */
.bookmark-health-checker {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background-color: var(--primary-bg);
  border-left: 1px solid var(--border-color);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.bookmark-health-checker.open {
  transform: translateX(0);
}

.health-checker-header {
  padding: var(--padding-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--secondary-bg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.health-checker-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
}

.health-checker-content {
  flex: 1;
  padding: var(--padding-lg);
  overflow-y: auto;
  background-color: var(--primary-bg);
}

.health-checker-header .close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.health-checker-header .close-btn:hover {
  background-color: var(--secondary-bg);
  color: var(--text-primary);
}

/* Health Checker Sections - Exact Import Panel Style */
.health-checker-section {
  margin-bottom: var(--padding-xl);
}

.health-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--padding-md);
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
}

.health-section-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--padding-lg);
  line-height: 1.5;
}

.health-controls {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
  margin-bottom: var(--padding-lg);
}

.control-group {
  display: flex;
  gap: var(--padding-sm);
  flex-wrap: wrap;
}

.check-all-btn,
.delete-all-btn {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md) var(--padding-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  flex: 1;
  justify-content: center;
  background-color: var(--secondary-bg);
  color: var(--text-primary);
}

.check-all-btn {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.check-all-btn:hover:not(:disabled) {
  background-color: var(--accent-secondary);
  border-color: var(--accent-secondary);
  transform: translateY(-1px);
}

.check-all-btn.checking {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.delete-all-btn {
  background-color: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.delete-all-btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-1px);
}

.delete-duplicates-btn {
  background-color: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md) var(--padding-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.delete-duplicates-btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-1px);
}

.check-all-btn:disabled,
.delete-all-btn:disabled,
.delete-duplicates-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Health Check Instruction Banner */
.health-instruction-banner {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  background-color: var(--info-bg, #e3f2fd);
  border: 1px solid var(--info-border, #90caf9);
  border-radius: var(--radius-md);
  color: var(--info-text, #1565c0);
  margin-bottom: var(--padding-md);
  font-size: 0.875rem;
}

.health-instruction-banner svg {
  color: var(--info-color, #2196f3);
  flex-shrink: 0;
}

/* Auto-check Control */
.auto-check-control {
  margin-top: var(--padding-md);
}

.auto-check-label {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
}

.auto-check-label input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.auto-check-label:hover {
  color: var(--text-primary);
}

.timeout-control {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  background-color: var(--secondary-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.timeout-control label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  margin: 0;
}

.timeout-control select {
  padding: var(--padding-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--primary-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
}

/* Health Checker Enhanced Styling - Import Panel Style */
.bookmark-health-checker .format-option.primary {
  background: var(--accent-color);
  color: white;
  border: 2px solid var(--accent-color);
}

.bookmark-health-checker .format-option.primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

.bookmark-health-checker .format-option.primary.active {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
  box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.25);
}

.bookmark-health-checker .format-option.danger {
  background: var(--error-color);
  color: white;
  border: 2px solid var(--error-color);
}

.bookmark-health-checker .format-option.danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.bookmark-health-checker .format-option.restart {
  background: #ff9800;
  color: white;
  border: 2px solid #ff9800;
}

.bookmark-health-checker .format-option.restart:hover {
  background: #f57c00;
  border-color: #f57c00;
}

/* Status banners matching import panel */
.bookmark-health-checker .status-banner {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--padding-md);
  font-size: 14px;
}

.bookmark-health-checker .status-banner.info {
  background: rgba(74, 158, 255, 0.1);
  border: 1px solid rgba(74, 158, 255, 0.3);
  color: var(--accent-color);
}

.bookmark-health-checker .status-banner.processing {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #f59e0b;
}

.status-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--padding-sm);
  margin-bottom: var(--padding-lg);
}

.status-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--padding-xs);
  padding: var(--padding-sm) var(--padding-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--secondary-bg);
  color: var(--text-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 40px;
}

.status-filter:hover {
  background-color: var(--primary-bg);
  border-color: var(--accent-primary);
}

.status-filter.active {
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

.status-filter.hidden {
  opacity: 0.4;
  background-color: var(--tertiary-bg);
}

.status-filter-content {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  flex: 1;
}

.progress-section {
  margin-bottom: var(--padding-lg);
}

.progress-bar {
  position: relative;
  height: 12px;
  background-color: var(--secondary-bg);
  border-radius: var(--radius-full);
  margin-bottom: var(--padding-sm);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.progress-fill {
  height: 100%;
  background-color: var(--accent-primary);
  transition: width 0.3s ease;
  border-radius: var(--radius-full);
}

.progress-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
  font-weight: 500;
}

.bookmark-health-list {
  max-height: 50vh;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--secondary-bg);
}

.bookmark-health-item {
  display: flex;
  align-items: flex-start;
  gap: var(--padding-md);
  padding: var(--padding-md);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--primary-bg);
  transition: background-color var(--transition-fast);
}

.bookmark-health-item:last-child {
  border-bottom: none;
}

.bookmark-health-item:hover {
  background-color: var(--secondary-bg);
}

.bookmark-health-item.success {
  border-left: 3px solid var(--success-500);
}

.bookmark-health-item.error {
  border-left: 3px solid var(--error-500);
}

.bookmark-health-item.timeout {
  border-left: 3px solid var(--warning-500);
}

.bookmark-health-item.duplicate {
  border-left: 3px solid var(--info-500);
}

.status-button {
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  cursor: pointer;
  padding: var(--padding-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
}

.status-button:hover:not(:disabled) {
  background-color: var(--primary-bg);
  border-color: var(--accent-primary);
  transform: translateY(-1px);
}

.status-display {
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  padding: var(--padding-sm);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
}

.status-icon {
  width: 16px;
  height: 16px;
}

.status-icon.unchecked {
  border: 2px solid var(--text-muted);
  background: transparent;
  border-radius: 50%;
}

.status-icon.checking {
  color: var(--warning-500);
}

.status-icon.success {
  color: var(--success-500);
}

.status-icon.timeout {
  color: var(--warning-500);
}

.status-icon.error {
  color: var(--error-500);
}

.status-icon.duplicate {
  color: var(--info-500);
}

.bookmark-info {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--padding-xs);
  word-break: break-word;
  font-size: 0.875rem;
  line-height: 1.4;
}

.bookmark-url {
  font-size: 0.75rem;
  color: var(--text-secondary);
  word-break: break-all;
  margin-bottom: var(--padding-xs);
  opacity: 0.8;
  /* CRITICAL: Prevent layout overflow from long URLs */
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* Allow breaking on hyphens and slashes for better wrapping */
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.bookmark-meta {
  font-size: 0.75rem;
  color: var(--text-muted);
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
}

/* Website Analysis Styling */
.bookmark-analysis {
  display: flex;
  flex-direction: column;
  gap: var(--padding-xs);
  margin-top: var(--padding-xs);
}

.website-type {
  font-size: 0.75rem;
  color: var(--accent-color);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
}

.content-tags-container {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-top: 4px;
}

.content-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 0.65rem;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 12px;
  white-space: nowrap;
  text-transform: capitalize;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.content-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.analysis-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 4px;
  font-size: 0.6rem;
}

.analysis-status {
  color: #22c55e;
  font-weight: 500;
}

.analysis-time {
  color: var(--text-muted);
  opacity: 0.7;
}

.bookmark-error {
  font-size: 0.75rem;
  color: var(--error-color);
  margin-top: var(--padding-xs);
  padding: var(--padding-xs) var(--padding-sm);
  background-color: var(--secondary-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.bookmark-duplicate {
  font-size: 0.75rem;
  color: var(--warning-color);
  margin-top: var(--padding-xs);
  padding: var(--padding-xs) var(--padding-sm);
  background-color: var(--secondary-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

/* Bookmark Card Flip Styles */
.bookmark-card-flip {
  perspective: 1000px;
  width: 100%;
  height: 280px;
  position: relative;
  /* Prevent animation interruption during updates */
  will-change: transform;
  backface-visibility: hidden;
}

.bookmark-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: left;
  transition: transform 0.6s ease-in-out;
  transform-style: preserve-3d;
  /* Optimize for animations */
  will-change: transform;
  backface-visibility: hidden;
}

.bookmark-card-flip.flipped .bookmark-card-inner {
  transform: rotateY(180deg);
}

/* Prevent layout shifts during summary generation */
.bookmark-card-flip.generating {
  pointer-events: none;
}

/* Allow interactions on back panel even during generation */
.bookmark-card-flip.generating .bookmark-card-back {
  pointer-events: auto !important;
}

.bookmark-card-flip.generating .bookmark-card-back .detailed-actions {
  pointer-events: auto !important;
}

.bookmark-card-flip.generating .bookmark-card-back a,
.bookmark-card-flip.generating .bookmark-card-back button {
  pointer-events: auto !important;
}

.bookmark-card-flip.generating .bookmark-card-inner {
  transition: transform 0.6s ease-in-out;
}

.bookmark-card-front,
.bookmark-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  background-color: var(--primary-bg);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast);
  cursor: pointer;
  user-select: none;
}

.bookmark-card-flip:hover .bookmark-card-front,
.bookmark-card-flip:hover .bookmark-card-back {
  box-shadow: var(--shadow-md);
}

.bookmark-card-back {
  transform: rotateY(180deg);
  background-color: var(--secondary-bg);
  cursor: default !important;
  /* Override pointer cursor for back panel */
}

/* Allow interaction with links and buttons on back panel */
.bookmark-card-back a,
.bookmark-card-back button {
  user-select: auto !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Ensure detailed content allows interaction */
.bookmark-detailed-content {
  pointer-events: auto !important;
}

/* Specific override for visit link button */
.bookmark-card-back .visit-link-btn,
.bookmark-card-back .detailed-actions a {
  user-select: auto !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  position: relative !important;
  z-index: 999 !important;
}

/* Ensure detailed actions container doesn't block clicks */
.bookmark-card-back .detailed-actions {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 100 !important;
}

.bookmark-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--padding-md);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--secondary-bg);
}

/* Redesigned Bookmark Card Front Panel - Modern & Clean */
.bookmark-card-front-redesigned {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
  display: flex;
  flex-direction: column;
  overflow: visible;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
  user-select: none;
  padding: var(--padding-lg);
  padding-right: calc(var(--padding-lg) + 20px);
}

/* Enhanced clickable indicator */
.bookmark-card-front-redesigned:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--accent-primary);
}

/* Add a subtle click indicator */
.bookmark-card-front-redesigned:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.bookmark-card-front-redesigned:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--accent-primary);
}

/* Header with Icon + Title */
.bookmark-header-redesigned {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--padding-md);
}

.bookmark-icon-title {
  display: flex;
  align-items: center;
  gap: var(--padding-md);
  flex: 1;
  min-width: 0;
}

.title-with-star {
  display: inline-flex;
  align-items: baseline;
  gap: 2px;
  flex: 1;
  min-width: 0;
  flex-wrap: wrap;
}

.bookmark-favicon-redesigned {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--primary-bg);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.bookmark-favicon-redesigned .favicon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.favicon-fallback-redesigned {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
}

.bookmark-title-redesigned {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
  display: inline;
  flex: 0 1 auto;
  min-width: 0;
}

.favorite-btn-inline {
  background: none;
  border: none;
  padding: 2px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  flex-shrink: 0;
  vertical-align: text-top;
  position: relative;
  top: 1px;
}

.favorite-btn-inline:hover {
  color: var(--warning-color);
  transform: scale(1.1);
}

.favorite-btn-inline.active {
  color: var(--warning-color);
}

/* Removed old action button styles - now using inline and bottom positioning */

/* Content Area */
.bookmark-content-redesigned {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
  margin-bottom: var(--padding-md);
}

.description-with-flip {
  display: inline-flex;
  align-items: baseline;
  gap: 2px;
  flex-wrap: wrap;
}

.bookmark-description-redesigned {
  margin: 0;
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.4;
  display: inline;
  flex: 0 1 auto;
  min-width: 0;
}

.flip-button-inline {
  background: none;
  border: 1px solid var(--border-color);
  padding: 2px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 2px;
  vertical-align: baseline;
  opacity: 0.7;
  white-space: nowrap;
}

.flip-button-inline:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
  transform: scale(1.05) rotate(180deg);
  opacity: 1;
}

.bookmark-url-redesigned {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
  padding: var(--padding-xs) var(--padding-sm);
  background-color: var(--secondary-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  width: fit-content;
  /* CRITICAL: Prevent layout overflow from long URLs */
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
  overflow-wrap: break-word;
  min-width: 0;
}

/* Footer with Collections and Meta - RESPONSIVE DESIGN */
.bookmark-footer-redesigned {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--padding-sm);
  margin-top: auto;
  padding-bottom: var(--padding-sm);
  /* CRITICAL: Prevent overflow from long collection names */
  min-width: 0;
  overflow: hidden;
}

.bookmark-collections-redesigned {
  display: flex;
  gap: var(--padding-xs);
  flex-wrap: wrap;
  /* CRITICAL: Allow shrinking and prevent overflow */
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.collection-redesigned {
  background-color: var(--secondary-bg);
  color: var(--text-secondary);
  padding: var(--padding-xs) var(--padding-sm);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid var(--border-color);
  /* CRITICAL: Smart text handling for long collection names */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  /* Prevent extremely long names from breaking layout */
  flex-shrink: 1;
}

.bookmark-meta-redesigned {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--padding-sm);
  /* CRITICAL: Always visible, never pushed outside panel */
  flex-shrink: 0;
  min-width: fit-content;
}

.meta-item-redesigned {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  font-size: 0.875rem;
  color: var(--text-muted);
  white-space: nowrap;
  /* CRITICAL: Ensure meta items don't break layout */
  flex-shrink: 0;
  min-width: fit-content;
}

/* RESPONSIVE DESIGN: Adaptive layout for different screen sizes */
@media (max-width: 768px) {
  .bookmark-footer-redesigned {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--padding-xs);
  }

  .bookmark-meta-redesigned {
    justify-content: flex-start;
    width: 100%;
  }

  .collection-redesigned {
    max-width: 100px;
    /* Smaller on mobile */
  }
}

@media (max-width: 480px) {
  .collection-redesigned {
    max-width: 80px;
    /* Even smaller on very small screens */
    font-size: 0.75rem;
  }

  .meta-item-redesigned {
    font-size: 0.75rem;
  }
}

/* Ultra-Dense Card Layout - Maximum Space Utilization */
.bookmark-card-front-redesigned.card-style {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--radius-md);
  border: 2px solid var(--border-color);
  background: var(--primary-bg);
  display: grid;
  grid-template-rows: auto 1fr auto auto;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
  user-select: none;
  padding: 4px;
  gap: 2px;
}

/* Classic Theme - Original Styling with Collection Color Borders */
.theme-classic .bookmark-card-front-redesigned {
  background-color: var(--secondary-bg) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all var(--transition-medium);
  /* Collection color border will be applied via inline styles - no !important to allow override */
}

.theme-classic .bookmark-card-front-redesigned:hover {
  transform: translateY(-2px) rotate(-1deg) !important;
  box-shadow: var(--shadow-lg) !important;
  z-index: 1;
  /* Border color will remain the collection color from inline styles */
}

/* Classic theme alternating hover rotation like original */
.theme-classic .bookmark-card-flip:nth-child(even) .bookmark-card-front-redesigned:hover {
  transform: translateY(-2px) rotate(1deg) !important;
}

/* Hide modern elements in classic theme */
.theme-classic .collection-dot-indicator,
.theme-classic .collection-dot-small {
  display: none !important;
}

/* Modern Theme - Enhanced Styling */
.theme-modern .bookmark-card-front-redesigned.card-style:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-width: 3px;
}

/* Collection Color Indicators - Modern Theme Only */
.theme-modern .collection-dot-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: all 0.2s ease;
}

.theme-modern .collection-dot-indicator:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.theme-modern .collection-dot-small {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  vertical-align: middle;
}

/* Enhanced border styling for collection colors - Modern Theme Only */
.theme-modern .bookmark-card-front-redesigned {
  transition: all 0.3s ease;
  position: relative;
}

.theme-modern .bookmark-card-front-redesigned:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  filter: brightness(1.02);
}

/* Ultra-compact header */
.bookmark-card-front-redesigned.card-style .bookmark-header-redesigned {
  display: grid;
  grid-template-columns: 28px 1fr auto;
  gap: 6px;
  align-items: center;
  margin: 0;
}

.bookmark-card-front-redesigned.card-style .bookmark-favicon-redesigned {
  width: 28px;
  height: 28px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.bookmark-card-front-redesigned.card-style .title-with-star {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
}

.bookmark-card-front-redesigned.card-style .bookmark-title-redesigned {
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1.1;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-card-front-redesigned.card-style .favorite-btn-inline {
  padding: 1px;
  min-width: 16px;
  min-height: 16px;
}

/* Compact content area */
.bookmark-card-front-redesigned.card-style .bookmark-content-redesigned {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin: 0;
  overflow: hidden;
}

.bookmark-card-front-redesigned.card-style .bookmark-description-redesigned {
  font-size: 0.7rem;
  line-height: 1.2;
  color: var(--text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.bookmark-card-front-redesigned.card-style .flip-button-inline {
  padding: 1px 2px;
  min-width: 14px;
  min-height: 14px;
  border-radius: var(--radius-xs);
  display: inline;
  vertical-align: baseline;
  white-space: nowrap;
}

.bookmark-card-front-redesigned.card-style .bookmark-url-redesigned {
  font-size: 0.6rem;
  padding: 1px 4px;
  background: var(--secondary-bg);
  border-radius: var(--radius-xs);
  width: fit-content;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* CRITICAL: Enhanced URL handling for very long URLs like dodi-repacks.site */
  word-break: break-all;
  overflow-wrap: break-word;
  /* Ensure container doesn't expand beyond bounds */
  min-width: 0;
  flex-shrink: 1;
}

/* Tags section - highly visible */
.bookmark-card-front-redesigned.card-style .bookmark-tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin: 2px 0;
}

.bookmark-card-front-redesigned.card-style .tag-chip {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  padding: 1px 4px;
  border-radius: var(--radius-xs);
  font-size: 0.6rem;
  font-weight: 500;
  white-space: nowrap;
  line-height: 1.2;
}

/* Ultra-compact footer - RESPONSIVE DESIGN */
.bookmark-card-front-redesigned.card-style .bookmark-footer-redesigned {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 4px;
  align-items: center;
  margin: 0;
  padding: 0;
  /* CRITICAL: Prevent overflow in compact layout */
  min-width: 0;
  overflow: hidden;
}

.bookmark-card-front-redesigned.card-style .bookmark-collections-redesigned {
  display: flex;
  gap: 2px;
  /* CRITICAL: Allow shrinking in compact layout */
  min-width: 0;
  overflow: hidden;
}

.bookmark-card-front-redesigned.card-style .collection-redesigned {
  font-size: 0.6rem;
  padding: 1px 4px;
  background: var(--secondary-bg);
  border-radius: var(--radius-xs);
  border: 1px solid var(--border-color);
  /* CRITICAL: Smart truncation for compact cards */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
  /* Smaller limit for compact cards */
  flex-shrink: 1;
}

.bookmark-card-front-redesigned.card-style .bookmark-meta-redesigned {
  display: flex;
  gap: 6px;
  font-size: 0.6rem;
  color: var(--text-muted);
  /* CRITICAL: Always visible in compact layout */
  flex-shrink: 0;
  min-width: fit-content;
}

.bookmark-card-front-redesigned.card-style .meta-item-redesigned {
  display: flex;
  align-items: center;
  gap: 2px;
  /* CRITICAL: Ensure compact meta items stay visible */
  flex-shrink: 0;
  min-width: fit-content;
  white-space: nowrap;
}

/* Removed compact NEW indicator - using left panel filter instead */

.bookmark-card-front-redesigned.card-style .new-badge {
  background: var(--success-color);
  color: white;
  padding: 1px 3px;
  border-radius: var(--radius-xs);
  font-size: 0.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 1px;
  line-height: 1;
}

.bookmark-card-front-redesigned.card-style .revert-btn {
  background: var(--error-color);
  color: white;
  border: none;
  padding: 1px;
  border-radius: var(--radius-xs);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 12px;
  min-height: 12px;
}

/* Compact menu for card-style */
.bookmark-card-front-redesigned.card-style .menu-container-bottom {
  position: absolute;
  bottom: 2px;
  right: 2px;
}

.bookmark-card-front-redesigned.card-style .menu-btn-bottom {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  padding: 1px;
  border-radius: var(--radius-xs);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  min-height: 16px;
  box-shadow: var(--shadow-xs);
}

/* Inline Menu Button and Container */
.meta-item-with-menu {
  display: inline-flex;
  align-items: baseline;
  gap: 2px;
}

.menu-btn-inline {
  background: none;
  border: 1px solid var(--border-color);
  padding: 2px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  flex-shrink: 0;
  vertical-align: text-top;
  position: relative;
  top: 1px;
  opacity: 0.7;
}

.menu-btn-inline:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
  opacity: 1;
}

.dropdown-menu-inline {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: var(--padding-xs);
  background-color: var(--primary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 100;
  min-width: 150px;
  padding: var(--padding-xs);
}

.dropdown-menu-inline .menu-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-sm);
  border: none;
  background: none;
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  width: 100%;
  text-align: left;
  transition: background-color var(--transition-fast);
}

.dropdown-menu-inline .menu-item:hover {
  background-color: var(--secondary-bg);
}

.dropdown-menu-inline .menu-item.danger {
  color: var(--error-color);
}

.dropdown-menu-inline .menu-separator {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: var(--padding-xs) 0;
}

/* Bottom Menu Container (default) */
.menu-container-bottom {
  position: absolute;
  bottom: var(--padding-md);
  right: var(--padding-md);
}

.menu-btn-bottom {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  padding: var(--padding-xs);
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.menu-btn-bottom:hover {
  background-color: var(--primary-bg);
  color: var(--text-primary);
  border-color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.dropdown-menu-bottom {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: var(--padding-xs);
  background-color: var(--primary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 100;
  min-width: 150px;
  padding: var(--padding-xs);
}

.dropdown-menu-bottom .menu-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-sm);
  border: none;
  background: none;
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  width: 100%;
  text-align: left;
  transition: background-color var(--transition-fast);
}

.dropdown-menu-bottom .menu-item:hover {
  background-color: var(--secondary-bg);
}

.dropdown-menu-bottom .menu-item.danger {
  color: var(--error-color);
}

.dropdown-menu-bottom .menu-separator {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: var(--padding-xs) 0;
}

.bookmark-actions {
  display: flex;
  gap: var(--padding-xs);
}

.action-btn {
  background: none;
  border: none;
  padding: var(--padding-xs);
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background-color: var(--primary-bg);
  color: var(--text-primary);
}

.favorite-btn.active {
  color: var(--warning-color);
}

.flip-btn {
  color: var(--accent-primary);
}

.flip-button-corner {
  position: absolute;
  bottom: var(--padding-sm);
  right: var(--padding-sm);
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
  z-index: 10;
}

.flip-button-corner:hover {
  background-color: var(--accent-secondary);
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.flip-button-corner:active {
  transform: scale(0.95);
}

.bookmark-content {
  flex: 1;
  padding: var(--padding-md);
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

.bookmark-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.bookmark-title a {
  color: var(--text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.bookmark-title a:hover {
  color: var(--accent-primary);
}

.bookmark-summary {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
  margin: 0;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bookmark-meta {
  display: flex;
  gap: var(--padding-md);
  font-size: 0.75rem;
  color: var(--text-muted);
}

.bookmark-meta span {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
}

.bookmark-tags {
  display: flex;
  gap: var(--padding-xs);
  flex-wrap: wrap;
}

.bookmark-tag {
  background-color: var(--secondary-bg);
  color: var(--text-secondary);
  padding: 2px var(--padding-xs);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  border: 1px solid var(--border-color);
}

.bookmark-tag.more {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

/* Back side specific styles */
.content-type-indicator {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-transform: capitalize;
}

.content-type-icon.youtube {
  color: #ff0000;
}

.content-type-icon.github {
  color: #333;
}

.content-type-icon.docs {
  color: var(--info-color);
}

.content-type-icon.article {
  color: var(--success-color);
}

.bookmark-detailed-content {
  flex: 1;
  padding: var(--padding-md);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
}

.detailed-summary h4,
.key-points h4,
.extracted-data h4,
.youtube-data h4 {
  margin: 0 0 var(--padding-sm) 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.detailed-summary p {
  font-size: 0.8rem;
  line-height: 1.4;
  color: var(--text-secondary);
  margin: 0;
}

.key-points ul {
  margin: 0;
  padding-left: var(--padding-lg);
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.key-points li {
  margin-bottom: var(--padding-xs);
  line-height: 1.3;
}

.data-grid {
  display: grid;
  gap: var(--padding-xs);
}

.data-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
}

.data-label {
  color: var(--text-muted);
  font-weight: 500;
}

.data-value {
  color: var(--text-secondary);
}

.youtube-info {
  display: flex;
  gap: var(--padding-sm);
}

.youtube-thumbnail {
  width: 60px;
  height: 45px;
  border-radius: var(--radius-sm);
  object-fit: cover;
}

.youtube-meta {
  flex: 1;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.youtube-meta p {
  margin: 0 0 var(--padding-xs) 0;
}

.detailed-actions {
  margin-top: auto;
  padding-top: var(--padding-sm);
  border-top: 1px solid var(--border-color);
  position: relative;
  /* Establish stacking context */
  z-index: 5;
  /* Ensure actions are above background elements */
}

.visit-link-btn {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  padding: var(--padding-sm) var(--padding-md);
  background-color: var(--accent-primary);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color var(--transition-fast);
  justify-content: center;
  user-select: auto;
  /* Allow text selection for links */
  cursor: pointer;
  /* Ensure pointer cursor for links */
  z-index: 10;
  /* Ensure link is above other elements */
  position: relative;
  /* Establish stacking context */
}

.visit-link-btn:hover {
  background-color: var(--accent-secondary);
}

.retry-btn {
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: var(--padding-sm) var(--padding-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.retry-btn:hover {
  background-color: var(--primary-bg);
  border-color: var(--accent-primary);
}

/* Summary Generation Panel Styles */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: var(--padding-md);
  margin-bottom: var(--padding-lg);
}

.stat-card {
  background-color: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--padding-md);
  text-align: center;
}

.stat-card.error {
  border-color: var(--error-color);
  background-color: rgba(239, 68, 68, 0.1);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--padding-xs);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.no-bookmarks-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--padding-xl);
  text-align: center;
  color: var(--success-color);
}

.no-bookmarks-message p {
  margin: var(--padding-sm) 0 var(--padding-xs) 0;
  font-weight: 500;
}

.no-bookmarks-message small {
  color: var(--text-secondary);
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.feature-item {
  display: flex;
  gap: var(--padding-md);
  padding: var(--padding-md);
  background-color: var(--secondary-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.feature-icon.youtube {
  background-color: rgba(255, 0, 0, 0.1);
}

.feature-icon.github {
  background-color: rgba(51, 51, 51, 0.1);
}

.feature-icon.docs {
  background-color: rgba(59, 130, 246, 0.1);
}

.feature-icon.webpage {
  background-color: rgba(16, 185, 129, 0.1);
}

.feature-content h4 {
  margin: 0 0 var(--padding-xs) 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.feature-content p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
  max-height: 200px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  align-items: flex-start;
  gap: var(--padding-sm);
  padding: var(--padding-sm);
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  color: var(--error-color);
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

.tip-item {
  padding: var(--padding-sm);
  background-color: var(--secondary-bg);
  border-left: 3px solid var(--accent-primary);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  line-height: 1.4;
  color: var(--text-secondary);
}

.tip-item strong {
  color: var(--text-primary);
}

/* Toast Notification Styles */
.toast-container {
  position: fixed;
  top: var(--padding-lg);
  right: var(--padding-lg);
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
  pointer-events: none;
}

.toast {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  background: var(--primary-bg);
  border-radius: var(--radius-lg);
  padding: var(--padding-md);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--accent-color);
  min-width: 300px;
  max-width: 400px;
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast.toast-visible {
  transform: translateX(0);
  opacity: 1;
}

.toast-success {
  border-left-color: var(--success-color);
}

.toast-error {
  border-left-color: #ef4444;
}

.toast-warning {
  border-left-color: #f59e0b;
}

.toast-info {
  border-left-color: var(--accent-color);
}

.toast-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.toast-success .toast-icon {
  color: var(--success-color);
}

.toast-error .toast-icon {
  color: #ef4444;
}

.toast-warning .toast-icon {
  color: #f59e0b;
}

.toast-info .toast-icon {
  color: var(--accent-color);
}

.toast-message {
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.toast-close:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--padding-xl);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-medium);
  background-color: var(--tertiary-bg);
  position: relative;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: var(--accent-color);
  background-color: rgba(74, 158, 255, 0.05);
}

.upload-area.dragging {
  border-color: var(--accent-color);
  background-color: rgba(74, 158, 255, 0.1);
  transform: scale(1.02);
}

.upload-area.processing {
  cursor: default;
  pointer-events: none;
}

.file-input {
  display: none;
}

.upload-text {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: var(--padding-sm);
}

.upload-hint {
  color: var(--text-muted);
  font-size: 14px;
}

.processing-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--tertiary-bg);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--padding-md);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--tertiary-bg);
  border-radius: 2px;
  overflow: hidden;
  margin-top: var(--padding-md);
}

.progress-fill {
  height: 100%;
  background-color: var(--accent-color);
  transition: width var(--transition-medium);
}

.success-icon {
  color: var(--success-color);
  margin-bottom: var(--padding-md);
}

.error-icon {
  color: var(--error-color);
  margin-bottom: var(--padding-md);
}

.template-actions {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

.template-btn {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  padding: var(--padding-md);
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  text-align: left;
  width: 100%;
}

.template-btn:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.browser-instructions {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: var(--padding-md);
  padding: var(--padding-md);
  background-color: var(--tertiary-bg);
  border-radius: var(--radius-lg);
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .bookmark-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .header-center {
    max-width: 400px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform var(--transition-medium);
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .header {
    padding: 0 var(--padding-lg);
  }

  .header-center {
    flex-direction: column;
    gap: var(--padding-md);
    align-items: stretch;
  }

  .bookmark-grid {
    grid-template-columns: 1fr;
    gap: var(--padding-md);
  }

  .bookmark-grid-container {
    padding: var(--padding-lg);
  }

  .import-panel {
    width: 100%;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform var(--transition-medium);
  }

  .import-panel.open {
    transform: translateX(0);
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    height: auto;
    padding: var(--padding-md);
    gap: var(--padding-md);
  }

  .header-left {
    align-self: flex-start;
  }

  .header-right {
    align-self: flex-end;
  }

  .bookmark-card {
    padding: var(--padding-md);
  }

  .empty-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Focus and Accessibility */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* Selection */
::selection {
  background-color: var(--accent-color);
  color: var(--text-primary);
}

/* Print Styles */
@media print {

  .sidebar,
  .header,
  .import-panel {
    display: none;
  }

  .main-content {
    margin: 0;
  }

  .bookmark-card {
    break-inside: avoid;
    margin-bottom: var(--padding-md);
  }
}

/* ========================================
   FINAL GLASS MORPHISM OVERRIDE - MAXIMUM PRIORITY
   This ensures sidebar glass morphism works regardless of other CSS files
   ======================================== */

/* Force Glass Morphism on Sidebar - Ultimate Override */
html body .bookmark-manager .sidebar,
html body aside.sidebar,
html body .sidebar,
.bookmark-manager aside,
aside[class*="sidebar"] {
  background: rgba(0, 0, 0, 0.3) !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  border-right: 2px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Force Glass Morphism on Sidebar Header - Ultimate Override */
html body .bookmark-manager .sidebar .sidebar-header,
html body .sidebar .sidebar-header,
html body .sidebar-header {
  background: rgba(0, 0, 0, 0.2) !important;
  background-color: rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Light Theme Ultimate Override */
[data-theme*="light"] html body .bookmark-manager .sidebar,
[data-theme*="light"] html body aside.sidebar,
[data-theme*="light"] html body .sidebar {
  background: rgba(255, 255, 255, 0.95) !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  border-right: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

[data-theme*="light"] html body .sidebar .sidebar-header {
  background: rgba(255, 255, 255, 0.8) !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

/* ========================================
   GLASS MORPHISM ENHANCEMENT - SPECIFIC CLASSES
   ======================================== */

/* Glass Morphism Sidebar - Specific Class with Maximum Priority */
.glass-morphism-sidebar,
aside.glass-morphism-sidebar,
.sidebar.glass-morphism-sidebar {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  border-right: 2px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  position: relative !important;
  z-index: 100 !important;
  transition: all 0.3s ease-in-out !important;
}

/* Glass Morphism Header - Specific Class */
.glass-morphism-header,
.sidebar-header.glass-morphism-header {
  background: rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Glass Morphism Content - Specific Class for Main Content Area */
.glass-morphism-content,
.sidebar-content.glass-morphism-content {
  background: rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  flex: 1 !important;
}

/* Light Theme Support for Glass Morphism Classes */
[data-theme*="light"] .glass-morphism-sidebar,
[data-theme*="light"] aside.glass-morphism-sidebar,
[data-theme*="light"] .sidebar.glass-morphism-sidebar {
  background: rgba(255, 255, 255, 0.95) !important;
  border-right: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

[data-theme*="light"] .glass-morphism-header,
[data-theme*="light"] .sidebar-header.glass-morphism-header {
  background: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

[data-theme*="light"] .glass-morphism-content,
[data-theme*="light"] .sidebar-content.glass-morphism-content {
  background: rgba(255, 255, 255, 0.85) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

/* Force glass morphism on any element with these classes */
.glass-morphism-sidebar * {
  color: inherit !important;
}

/* Browser compatibility fallback */
@supports not (backdrop-filter: blur(1px)) {
  .glass-morphism-sidebar {
    background: rgba(0, 0, 0, 0.8) !important;
  }

  [data-theme*="light"] .glass-morphism-sidebar {
    background: rgba(255, 255, 255, 0.95) !important;
  }
}

/* Cache Management Styles */
.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  transition: all 0.2s ease !important;
}

.refresh-btn:hover {
  transform: rotate(180deg) scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.import-another-btn {
  margin-top: 16px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.import-another-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Delete Dialog Styles */
.delete-cancel-btn {
  padding: 12px 24px;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
  min-height: 44px;
  /* Mobile-friendly touch target */
}

.delete-cancel-btn:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.delete-confirm-btn {
  padding: 12px 24px;
  background-color: var(--error-color);
  border: none;
  color: white;
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px;
  /* Mobile-friendly touch target */
}

.delete-confirm-btn:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.delete-confirm-btn:active {
  transform: translateY(0);
}

/* Simple Delete Dialog Styles */
.simple-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.simple-dialog-content {
  background: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 425px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Simple Dialog - Light Themes */
[data-theme*="light"] .simple-dialog-content {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

.simple-dialog-header {
  padding: 24px 24px 0 24px;
}

.simple-dialog-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.simple-dialog-icon {
  width: 20px;
  height: 20px;
  color: var(--error-color);
}

.simple-dialog-body {
  padding: 16px 24px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.simple-dialog-body p {
  margin-bottom: 12px;
}

.simple-dialog-body p:last-child {
  margin-bottom: 0;
}

.simple-dialog-footer {
  padding: 0 24px 24px 24px;
  display: flex;
  gap: 12px;
  flex-direction: column-reverse;
}

@media (min-width: 640px) {
  .simple-dialog-footer {
    flex-direction: row;
    justify-content: flex-end;
  }
}

.simple-dialog-cancel {
  padding: 12px 24px;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
  min-height: 44px;
}

.simple-dialog-cancel:hover {
  background-color: var(--tertiary-bg);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.simple-dialog-confirm {
  padding: 12px 24px;
  background-color: var(--error-color);
  border: none;
  color: white;
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px;
}

.simple-dialog-confirm:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* File Replace Notification */
.file-replace-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInRight 0.3s ease-out;
}

.notification-content {
  padding: 20px;
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.notification-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.notification-text {
  color: white;
  line-height: 1.4;
}

.notification-text strong {
  display: block;
  font-size: 16px;
  margin-bottom: 8px;
}

.notification-text p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.notification-text small {
  font-size: 12px;
  opacity: 0.9;
}

.notification-text code {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Auto-save Indicator */
.auto-save-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.auto-save-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.auto-save-indicator.enabled {
  color: #10b981;
}

.auto-save-indicator.disabled {
  color: #ef4444;
}

.auto-save-icon {
  font-size: 14px;
}

.auto-save-text {
  white-space: nowrap;
}

.auto-save-toggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 2px;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.auto-save-toggle:hover {
  transform: scale(1.1);
}

.auto-save-toggle:active {
  transform: scale(0.95);
}

/* File Attachment Indicator - Matching existing design */
.file-attachment-container {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  margin-right: var(--padding-md);
}

.file-attachment-badge {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: pulse-badge 2s ease-in-out infinite;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
  display: flex;
  align-items: center;
  gap: 4px;
  max-width: 120px;
}

.file-attachment-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

.save-mode-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.save-mode-btn:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.save-mode-btn.new {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.save-mode-btn.new:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.detach-file-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: var(--radius-full);
  font-size: 10px;
  font-weight: 600;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.detach-file-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
}

/* Add Bookmark Modal */
.add-bookmark-modal {
  max-width: 600px;
  width: 90vw;
}

.add-bookmark-form {
  display: flex;
  flex-direction: column;
  gap: var(--padding-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--padding-xs);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--padding-md);
}

.form-label {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  font-weight: 500;
  color: var(--text-color);
  font-size: 14px;
}

.form-input,
.form-textarea,
.form-select {
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--card-bg);
  color: var(--text-color);
  font-size: 14px;
  transition: all var(--transition-fast);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color);
}

.form-checkbox input[type="checkbox"] {
  margin: 0;
  margin-right: 4px;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .add-bookmark-modal {
    width: 95vw;
    margin: 20px;
  }
}

/* Drag/Paste Hint */
.drag-paste-hint {
  display: flex;
  align-items: center;
  gap: var(--padding-xs);
  padding: 12px 16px;
  background: rgba(59, 130, 246, 0.1);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: var(--radius-md);
  color: rgba(59, 130, 246, 0.8);
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.drag-paste-hint:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.5);
  color: rgba(59, 130, 246, 1);
}

.drag-paste-hint.primary {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-color: rgba(59, 130, 246, 0.4);
  color: rgba(59, 130, 246, 0.9);
}

.drag-paste-hint.primary:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2));
  border-color: rgba(59, 130, 246, 0.6);
  color: rgba(59, 130, 246, 1);
  transform: translateY(-1px);
}

/* Context Menu */
.context-menu {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 4px 0;
  min-width: 120px;
  backdrop-filter: blur(10px);
  animation: contextMenuFadeIn 0.15s ease-out;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.context-menu-item:hover {
  background: var(--accent-color);
  color: white;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Memory Optimization Styles */
.memory-optimization-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
  padding: 2px 6px;
  background: rgba(34, 197, 94, 0.1);
  color: rgba(34, 197, 94, 0.8);
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: help;
}

.bookmark-grid-wrapper.virtual-scroll {
  contain: layout style paint;
  will-change: scroll-position;
}

.bookmark-grid-wrapper.virtual-scroll .bookmark-grid {
  contain: layout style paint;
}

/* Optimize bookmark cards for large datasets */
.bookmark-card {
  contain: layout style paint;
  will-change: transform;
}

.bookmark-card:hover {
  contain: layout style paint;
}

/* Performance optimizations for large lists */
.virtual-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.virtual-scroll::-webkit-scrollbar {
  width: 8px;
}

.virtual-scroll::-webkit-scrollbar-track {
  background: var(--border-color);
  border-radius: 4px;
}

.virtual-scroll::-webkit-scrollbar-thumb {
  background: var(--text-secondary);
  border-radius: 4px;
}

.virtual-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--text-color);
}

/* Removed mobile responsive fixes for recently-added-badge - using left panel filter instead */

/* Enhanced Localization Button Designs */
.localization-toggle-enhanced {
  position: relative;
  overflow: hidden;
}

.localization-toggle-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.localization-toggle-enhanced:hover::before {
  left: 100%;
}

/* Pulse animation for indicator dot */
@keyframes pulse {

  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Alternative Design Options */
.localization-option-minimal {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  height: 36px;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.localization-option-minimal:hover {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 158, 255, 0.3);
}

.localization-option-badge {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, var(--tertiary-bg), var(--secondary-bg));
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  height: 44px;
  transition: all var(--transition-fast);
  overflow: hidden;
}

.localization-option-badge::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.localization-option-badge:hover::after {
  transform: translateX(100%);
}

.localization-option-badge:hover {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  color: white;
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 158, 255, 0.4);
}

.localization-option-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 16px;
  transition: all var(--transition-fast);
  position: relative;
}

.localization-option-compact:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  transform: scale(1.05) rotate(5deg);
  box-shadow: 0 6px 20px rgba(74, 158, 255, 0.3);
}

.localization-option-compact::before {
  content: attr(data-locale);
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: var(--accent-color);
  color: white;
  font-size: 8px;
  font-weight: 600;
  padding: 1px 3px;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* 🚀 REVOLUTIONARY MIND MAP ANIMATIONS - Ultra-Advanced Visualizations */

/* AI Particle Flow System Animations */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes particleGlow {

  0%,
  100% {
    filter: drop-shadow(0 0 5px currentColor);
  }

  50% {
    filter: drop-shadow(0 0 15px currentColor);
  }
}

@keyframes aiClusterPulse {

  0%,
  100% {
    background: rgba(74, 158, 255, 0.1);
    border-color: rgba(74, 158, 255, 0.3);
  }

  50% {
    background: rgba(74, 158, 255, 0.2);
    border-color: rgba(74, 158, 255, 0.5);
  }
}

/* Neural Network Graph Animations */
@keyframes neuralPulse {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }

  100% {
    opacity: 0;
    transform: scale(0);
  }
}

@keyframes brainWave {

  0%,
  100% {
    filter: drop-shadow(0 0 3px #00ff88);
  }

  50% {
    filter: drop-shadow(0 0 10px #00ff88);
  }
}

/* 3D Constellation Animations */
@keyframes starTwinkle {

  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }

  25% {
    opacity: 1;
    transform: scale(1.2);
  }

  75% {
    opacity: 0.8;
    transform: scale(0.9);
  }
}

@keyframes cosmicGlow {

  0%,
  100% {
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.3);
  }

  50% {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.6);
  }
}

/* Revolutionary Design Status Indicators */
.ai-status-indicator {
  animation: aiClusterPulse 2s ease-in-out infinite;
}

.neural-activity {
  animation: brainWave 1.5s ease-in-out infinite;
}

.cosmic-element {
  animation: cosmicGlow 3s ease-in-out infinite;
}

/* 🌟 COSMIC CONSTELLATION FOCUS ANIMATIONS */
@keyframes cosmicFadeIn {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
    transform: scale(0.9);
  }

  100% {
    opacity: 1;
    backdrop-filter: blur(10px);
    transform: scale(1);
  }
}

@keyframes cosmicSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes cosmicPulse {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.4);
  }

  50% {
    box-shadow: 0 0 40px rgba(138, 43, 226, 0.8);
  }
}

@keyframes galaxyRotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes nebulaShimmer {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

/* Enhanced star twinkle for constellation focus */
@keyframes starTwinkle {

  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
    filter: brightness(1);
  }

  25% {
    opacity: 1;
    transform: scale(1.2);
    filter: brightness(1.5);
  }

  50% {
    opacity: 0.8;
    transform: scale(0.9);
    filter: brightness(0.8);
  }

  75% {
    opacity: 1;
    transform: scale(1.1);
    filter: brightness(1.3);
  }
}

/* Cosmic bookmark card effects */
.cosmic-bookmark-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.8) 100%);
  border: 1px solid rgba(138, 43, 226, 0.6);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.cosmic-bookmark-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.cosmic-bookmark-card:hover::before {
  left: 100%;
}

.cosmic-bookmark-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6);
  border-color: #8a2be2;
}

/* Constellation focus overlay */
.constellation-focus-overlay {
  background: rgba(10, 10, 46, 0.95);
  backdrop-filter: blur(10px);
  animation: cosmicFadeIn 0.8s ease-out;
}

.constellation-header {
  animation: starTwinkle 2s ease-in-out infinite;
}

.constellation-title {
  background: linear-gradient(45deg, #8a2be2, #ffffff);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  font-weight: bold;
}

/* Performance Optimizations for Advanced Visualizations */
.particle-canvas,
.neural-canvas,
.constellation-canvas {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* GPU Acceleration for Cosmic Effects */
.cosmic-element {
  will-change: transform, opacity, filter;
  transform: translate3d(0, 0, 0);
}

.cosmic-glow {
  filter: drop-shadow(0 0 10px currentColor);
  animation: cosmicPulse 3s ease-in-out infinite;
}

/* ========================================
   MULTIMEDIA ORGANIZATION PANEL STYLING
   ======================================== */

/* Multimedia Panel Base */
.multimedia-panel {
  /* Inherits from import-panel styling above */
}

/* Multimedia Sections - Glass Morphism */
.multimedia-section {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Multimedia Sections - Light Themes */
[data-theme*="light"] .multimedia-section {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.multimedia-section__header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.multimedia-section__icon {
  color: #4A9EFF;
}

.multimedia-section__title {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 16px;
  margin: 0;
}

.multimedia-section__description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

/* Multimedia Text - Light Themes */
[data-theme*="light"] .multimedia-section__title {
  color: rgba(0, 0, 0, 0.9);
}

[data-theme*="light"] .multimedia-section__description {
  color: rgba(0, 0, 0, 0.7);
}

/* Multimedia Grid */
.multimedia-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.multimedia-grid--4-col {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

/* Multimedia Cards - Glass Morphism */
.multimedia-card {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 8px;
  transition: all 0.2s ease;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.multimedia-card:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
}

/* Multimedia Cards - Light Themes */
[data-theme*="light"] .multimedia-card {
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

[data-theme*="light"] .multimedia-card:hover {
  background: rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.12);
}

.multimedia-card--info {
  flex-direction: row;
  text-align: left;
  align-items: flex-start;
}

.multimedia-card__icon {
  background: var(--accent-color);
  color: #ffffff;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.multimedia-card__icon--video {
  background: #ef4444;
}

.multimedia-card__icon--audio {
  background: #8b5cf6;
}

.multimedia-card__icon--document {
  background: #06b6d4;
}

.multimedia-card__icon--ai {
  background: #f59e0b;
}

.multimedia-card__content {
  flex: 1;
}

.multimedia-card__title {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 14px;
  margin: 0 0 4px 0;
}

.multimedia-card__description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 0;
}

/* Multimedia Card Text - Light Themes */
[data-theme*="light"] .multimedia-card__title {
  color: rgba(0, 0, 0, 0.9);
}

[data-theme*="light"] .multimedia-card__description {
  color: rgba(0, 0, 0, 0.7);
}

/* Multimedia Buttons - Glass Morphism */
.multimedia-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.multimedia-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Multimedia Buttons - Light Themes */
[data-theme*="light"] .multimedia-btn {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.9);
}

[data-theme*="light"] .multimedia-btn:hover {
  background: rgba(0, 0, 0, 0.08);
  border-color: rgba(0, 0, 0, 0.15);
}

.multimedia-btn--primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

.multimedia-btn--primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

.multimedia-btn--secondary {
  background: var(--secondary-bg);
  border-color: var(--border-color);
}

.multimedia-btn--danger {
  background: var(--error-color);
  border-color: var(--error-color);
  color: #ffffff;
}

.multimedia-btn--danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.multimedia-btn--disabled {
  background: var(--border-color);
  border-color: var(--border-color);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

.multimedia-btn--sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Radio Options */
.multimedia-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.multimedia-radio-option {
  background: var(--tertiary-bg);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.multimedia-radio-option:hover {
  border-color: var(--border-hover);
}

.multimedia-radio-option--active {
  border-color: var(--accent-color);
  background: var(--secondary-bg);
}

.multimedia-radio-option__input {
  display: none;
}

.multimedia-radio-option__content {
  flex: 1;
}

.multimedia-radio-option__header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.multimedia-radio-option__icon {
  font-size: 16px;
}

.multimedia-radio-option__title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
  margin: 0;
}

.multimedia-radio-option__description {
  color: var(--text-secondary);
  font-size: 12px;
  margin: 0;
}

.multimedia-radio-option__indicator {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  position: relative;
}

.multimedia-radio-option--active .multimedia-radio-option__indicator {
  border-color: var(--accent-color);
}

.multimedia-radio-option--active .multimedia-radio-option__indicator::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 8px;
  height: 8px;
  background: var(--accent-color);
  border-radius: 50%;
}

/* Multimedia Forms */
.multimedia-form-group {
  margin-bottom: 16px;
}

.multimedia-form-label {
  display: block;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 14px;
}

.multimedia-form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--tertiary-bg);
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
}

.multimedia-form-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

.multimedia-form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--tertiary-bg);
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
  transition: all 0.2s ease;
  resize: vertical;
  min-height: 80px;
}

.multimedia-form-textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 59, 130, 246), 0.1);
}

/* Multimedia Bookmark Selector */
.multimedia-bookmark-selector {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.multimedia-bookmark-list {
  max-height: 400px;
  overflow-y: auto;
}

.multimedia-collection-group {
  margin-bottom: 16px;
}

.multimedia-collection-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--tertiary-bg);
  border-radius: 6px;
  margin-bottom: 8px;
}

.multimedia-collection-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.multimedia-collection-icon {
  font-size: 14px;
}

.multimedia-collection-name {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.multimedia-collection-count {
  color: var(--text-secondary);
  font-size: 12px;
}

.multimedia-collection-actions {
  display: flex;
  gap: 8px;
}

.multimedia-collection-select-btn {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.multimedia-collection-select-btn:hover {
  background: var(--secondary-bg);
  border-color: var(--border-hover);
}

.multimedia-collection-select-btn--selected {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

.multimedia-collection-select-btn--partial {
  background: var(--warning-color);
  border-color: var(--warning-color);
  color: #ffffff;
}

.multimedia-collection-bookmarks {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 20px;
}

.multimedia-bookmark-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.multimedia-bookmark-item:hover {
  background: var(--secondary-bg);
  border-color: var(--border-hover);
}

.multimedia-bookmark-item--selected {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: #ffffff;
}

.multimedia-bookmark-checkbox {
  width: 16px;
  height: 16px;
  accent-color: var(--accent-color);
}

.multimedia-bookmark-content {
  flex: 1;
  min-width: 0;
}

.multimedia-bookmark-title {
  color: inherit;
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.multimedia-bookmark-url {
  color: inherit;
  opacity: 0.7;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.multimedia-bookmark-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.multimedia-selected-indicator {
  color: var(--success-color);
  font-weight: bold;
}

.multimedia-bookmark-limit-notice {
  background: var(--warning-color);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  text-align: center;
  margin-top: 12px;
}

/* Results and Playlist Display */
.multimedia-results-container {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.multimedia-result-item {
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
  padding: 4px 0;
}

.multimedia-playlist-info {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.multimedia-playlist-items {
  margin-top: 16px;
}

.multimedia-playlist-manager {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.multimedia-saved-playlist-item {
  margin-bottom: 12px;
}