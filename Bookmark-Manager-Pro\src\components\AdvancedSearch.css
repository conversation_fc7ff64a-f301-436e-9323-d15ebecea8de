/* Advanced Search Component Styles */
.advanced-search {
  width: 100%;
  max-width: 1000px;
  margin: 0;
  padding: 0;
  /* Remove any default padding */
  position: relative;
  height: 44px;
  /* Match header button height */
  display: flex;
  align-items: flex-end;
  /* Bottom align with header */
}

/* Search Form - Aligned with Header Design System */
.search-form {
  display: flex;
  gap: 2px;
  /* Minimal gap between search input and search button */
  align-items: flex-end;
  /* Bottom align with header functions */
  margin-bottom: 0;
  /* Remove margin that pushes elements up */
  height: 44px;
  /* Match header button height */
}

.search-input-container {
  flex: 1.33;
  position: relative;
  height: 44px;
  /* Match header button height */
  display: flex;
  align-items: flex-end;
  /* Bottom align */
  margin: 0;
  /* Remove any margins */
  padding: 0;
  /* Remove any padding */
  min-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: flex-end;
  /* Bottom align with header functions */
  height: 44px;
  /* Match header button height */
}

.search-input {
  width: 100%;
  height: 44px;
  /* Match header button height exactly */
  padding: 0 60px 0 20px;
  /* Increased left padding by 1/5 (16px → 20px) */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: 16px;
  /* Increased by ~1/5 (14px → 16px) */
  font-weight: 400;
  /* Slightly lighter for input text */
  font-family: inherit;
  transition: all var(--transition-fast);
  outline: none;
  box-sizing: border-box;
  vertical-align: top;
  /* Ensure proper alignment */
}

.search-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-alpha);
}

.search-input::placeholder {
  color: var(--text-muted);
}

/* Search Loading Spinner */
.search-loading {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color, #333);
  border-top: 2px solid var(--primary-color, #007acc);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Clear Search Button - Compact to match header icons */
.clear-search-btn {
  position: absolute;
  right: 32px;
  /* Adjusted for smaller advanced button */
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 12px;
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  /* Much smaller, icon-only */
  height: 20px;
  /* Much smaller, icon-only */
}

.clear-search-btn:hover {
  color: var(--text-primary);
  background: var(--secondary-bg);
  border-radius: var(--radius-sm);
}

.clear-search-btn:focus {
  outline: 1px solid var(--accent-color);
  outline-offset: 1px;
}

/* Advanced Toggle Button - Compact to match header icons */
.advanced-toggle-btn {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 12px;
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  /* Much smaller, icon-only */
  height: 20px;
  /* Much smaller, icon-only */
}

.advanced-toggle-btn:hover,
.advanced-toggle-btn.active {
  color: var(--accent-color);
  background: var(--secondary-bg);
  border-radius: var(--radius-sm);
}

.advanced-toggle-btn:focus {
  outline: 1px solid var(--accent-color);
  outline-offset: 1px;
}

/* Search Submit Button - Match Header Button System */
.search-submit-btn {
  padding: 12px;
  background: var(--secondary-bg);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  /* Match header button width */
  height: 44px;
  /* Match header button height */
  min-width: 44px;
}

.search-submit-btn:hover {
  background: var(--accent-color);
  color: var(--text-primary);
  border-color: var(--accent-color);
}

.search-submit-btn:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

.search-submit-btn:active {
  background: var(--accent-color-dark);
}

/* Search Suggestions */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--dropdown-bg, #2a2a2a);
  border: 1px solid var(--border-color, #333);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color, rgba(0, 0, 0, 0.3));
  z-index: 1001;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;
  max-width: 100%;
  box-sizing: border-box;
}

.suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color, #333);
  transition: background-color 0.2s ease;
  color: var(--text-color, #ffffff);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background: var(--hover-bg, rgba(0, 122, 204, 0.1));
}

.suggestion-item mark {
  background: var(--highlight-bg, rgba(255, 255, 0, 0.3));
  color: var(--highlight-text, #ffffff);
  padding: 0 2px;
  border-radius: 2px;
}

/* Advanced Options */
.advanced-options {
  background: var(--primary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--padding-lg);
  margin-top: 8px;
  animation: slideDown 0.3s ease;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  max-height: 400px;
  overflow-y: auto;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--padding-lg);
  margin-bottom: var(--padding-lg);
}

.option-group h4 {
  margin: 0 0 var(--padding-md) 0;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
  margin-bottom: var(--padding-sm);
  cursor: pointer;
  color: var(--text-primary);
  font-size: 14px;
}

.option-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--accent-color);
  cursor: pointer;
}

.option-item .number-input {
  width: 80px;
  padding: var(--padding-xs) var(--padding-sm);
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: 14px;
  margin-left: auto;
}

.option-item .number-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-alpha);
}

/* Search History */
.search-history {
  border-top: 1px solid var(--border-color, #333);
  padding-top: 20px;
}

.search-history h4 {
  margin: 0 0 12px 0;
  color: var(--text-color, #ffffff);
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.history-item {
  padding: 6px 12px;
  background: var(--tag-bg, rgba(0, 122, 204, 0.1));
  color: var(--primary-color, #007acc);
  border: 1px solid var(--primary-color, #007acc);
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item:hover {
  background: var(--primary-color, #007acc);
  color: white;
  transform: translateY(-1px);
}

.history-item:focus {
  outline: 2px solid var(--primary-color, #007acc);
  outline-offset: 2px;
}

.clear-history-btn {
  padding: 6px 12px;
  background: none;
  color: var(--text-muted, #888);
  border: 1px solid var(--border-color, #333);
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-history-btn:hover {
  color: var(--error-color, #ff4444);
  border-color: var(--error-color, #ff4444);
}

.clear-history-btn:focus {
  outline: 2px solid var(--error-color, #ff4444);
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    gap: 12px;
  }

  .search-submit-btn {
    width: 100%;
    justify-content: center;
  }

  .options-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .search-input {
    font-size: 18px;
    /* Increased for mobile (was 16px, now ~1/5 larger) */
  }

  .history-items {
    justify-content: center;
  }

  .advanced-options {
    margin-left: -10px;
    margin-right: -10px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .search-suggestions {
    left: -10px;
    right: -10px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}

@media (max-width: 480px) {
  .advanced-options {
    padding: 16px;
  }

  .search-input {
    padding: 12px 70px 12px 15px;
    /* Increased padding by ~1/5 */
  }

  .option-item {
    font-size: 13px;
  }

  .history-item {
    max-width: 120px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .search-input {
    border-width: 3px;
  }

  .suggestion-item.selected {
    background: var(--primary-color, #007acc);
    color: white;
  }

  .history-item:hover {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {

  .search-input,
  .clear-search-btn,
  .advanced-toggle-btn,
  .search-submit-btn,
  .suggestion-item,
  .history-item {
    transition: none;
  }

  .advanced-options {
    animation: none;
  }

  .spinner {
    animation: none;
  }

  .search-submit-btn:hover,
  .history-item:hover {
    transform: none;
  }
}

/* Dark Theme Variables (default) */
.advanced-search {
  --primary-color: var(--accent-primary, #007acc);
  --primary-hover: var(--accent-primary-hover, #005a9e);
  --primary-color-alpha: var(--accent-primary-alpha, rgba(0, 122, 204, 0.1));
  --text-color: var(--text-primary, #ffffff);
  --text-muted: var(--text-secondary, #888);
  --input-bg: var(--surface-secondary, #1a1a1a);
  --card-bg: var(--surface-primary, #1e1e1e);
  --dropdown-bg: var(--surface-elevated, #2a2a2a);
  --border-color: var(--border-primary, #333);
  --hover-bg: var(--surface-hover, rgba(255, 255, 255, 0.1));
  --shadow-color: var(--shadow-primary, rgba(0, 0, 0, 0.3));
  --highlight-bg: rgba(255, 255, 0, 0.3);
  --highlight-text: var(--text-primary, #ffffff);
  --tag-bg: var(--accent-primary-alpha, rgba(0, 122, 204, 0.1));
  --error-color: var(--status-error, #ff4444);
}

/* Light Theme Variables */
[data-theme="light"] .advanced-search {
  --primary-color: var(--accent-primary, #0066cc);
  --primary-hover: var(--accent-primary-hover, #0052a3);
  --primary-color-alpha: var(--accent-primary-alpha, rgba(0, 102, 204, 0.1));
  --text-color: var(--text-primary, #333333);
  --text-muted: var(--text-secondary, #666);
  --input-bg: var(--surface-secondary, #ffffff);
  --card-bg: var(--surface-primary, #f8f9fa);
  --dropdown-bg: var(--surface-elevated, #ffffff);
  --border-color: var(--border-primary, #ddd);
  --hover-bg: var(--surface-hover, rgba(0, 0, 0, 0.05));
  --shadow-color: var(--shadow-primary, rgba(0, 0, 0, 0.1));
  --highlight-bg: rgba(255, 255, 0, 0.5);
  --highlight-text: var(--text-primary, #333333);
  --tag-bg: var(--accent-primary-alpha, rgba(0, 102, 204, 0.1));
  --error-color: var(--status-error, #dc3545);
}