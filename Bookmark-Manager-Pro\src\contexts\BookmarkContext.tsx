import React, { create<PERSON>ontext, ReactNode, useContext, useEffect, useState } from 'react'
import { Bookmark, Collection, ExportFormat, FilterType, Playlist, SplitCriteria } from '../../types'
import { getCollectionColor } from '../utils/collectionColors'
import { optimizedFilter } from '../utils/optimizedFiltering'
import { searchBookmarks, SearchOptions } from '../utils/searchUtils'

interface BookmarkData {
  bookmarks: Bookmark[]
  collections: Collection[]
  tags: string[]
  playlists: Playlist[]
}

interface BookmarkContextType {
  bookmarks: Bookmark[]
  collections: Collection[]
  tags: string[]
  playlists: Playlist[]
  filteredBookmarks: Bookmark[]
  selectedBookmarks: Bookmark[] // Bookmarks selected for split functionality
  searchQuery: string
  searchOptions: SearchOptions
  selectedCollection: string
  selectedTags: string[]
  selectedPlaylist: string | null
  filterType: FilterType
  isLoading: boolean
  isSelectMode: boolean // For selecting bookmarks to split
  setSearchQuery: (query: string) => void
  setSearchOptions: (options: SearchOptions) => void
  setSelectedCollection: (collection: string) => void
  setSelectedTags: (tags: string[]) => void
  setSelectedPlaylist: (playlistId: string | null) => void
  setFilterType: (type: FilterType) => void
  toggleBookmarkFavorite: (id: string) => void
  deleteBookmark: (id: string) => void
  addBookmark: (bookmark: Omit<Bookmark, 'id'>) => void
  addBookmarksBulk: (bookmarks: Omit<Bookmark, 'id'>[]) => void
  updateBookmark: (id: string, updates: Partial<Bookmark>) => void
  incrementVisitCount: (id: string) => void
  clearAllBookmarks: () => void
  replaceAllBookmarks: (bookmarks: Omit<Bookmark, 'id'>[], fileInfo?: { name: string, lastModified: number }) => void
  importBookmarks: (fileContent: string, format: 'html' | 'json' | 'csv', fileInfo?: { name: string, lastModified: number }, overwrite?: boolean) => Promise<{ success: boolean, count: number, message?: string }> // Import functionality
  toggleSelectMode: () => void // Toggle selection mode for split functionality
  toggleBookmarkSelection: (id: string) => void // Select/deselect bookmark for split
  selectAllBookmarks: () => void // Select all currently filtered bookmarks
  deselectAllBookmarks: () => void // Deselect all bookmarks
  deleteSelectedBookmarks: () => void // Delete all selected bookmarks
  addSelectedToPlaylist: (playlistId: string) => void // Add selected bookmarks to playlist
  exportBookmarks: (format: ExportFormat, filename: string, bookmarks?: Bookmark[]) => void // Export functionality
  splitBookmarks: (criteria: SplitCriteria, value?: string) => { [key: string]: Bookmark[] } // Split functionality
  // Playlist functionality
  createPlaylist: (name: string, description: string, color: string, bookmarkIds?: string[]) => void
  updatePlaylist: (id: string, updates: Partial<Omit<Playlist, 'id'>>) => void
  deletePlaylist: (id: string) => void
  addBookmarkToPlaylist: (bookmarkId: string, playlistId: string) => void
  removeBookmarkFromPlaylist: (bookmarkId: string, playlistId: string) => void
  getPlaylistBookmarks: (playlistId: string) => Bookmark[]
  // Drag & Drop functionality
  addBookmarkFromDrop: (bookmark: Omit<Bookmark, 'id'>) => Promise<string>
  revertRecentBookmark: (id: string) => void
  clearRecentStatus: (id: string) => void
  // Save functionality
  saveBookmarks: () => Promise<void>
  hasUnsavedChanges: boolean
  autoSaveEnabled: boolean
  setAutoSaveEnabled: (enabled: boolean) => void
  // Auto-organize functionality
  autoOrganizeBookmarks: (options: { strategy: string, preserveExistingFolders: boolean, useAI: boolean }) => Promise<{ success: boolean, summary: string, foldersCreated: string[], bookmarksMoved: number }>
  previewAutoOrganize: (options: { strategy: string, preserveExistingFolders: boolean, useAI: boolean }) => Promise<{ foldersToCreate: string[], bookmarksToMove: { id: string, currentFolder: string, newFolder: string }[], summary: string }>
  // File attachment
  attachedFile: { name: string, lastModified: number } | null
  saveMode: 'update' | 'new'
  setSaveMode: (mode: 'update' | 'new') => void
  detachFile: () => void
  // Cache management
  clearCacheAndRefresh: () => void
}

const BookmarkContext = createContext<BookmarkContextType | undefined>(undefined)

export const useBookmarks = () => {
  const context = useContext(BookmarkContext)
  if (!context) {
    throw new Error('useBookmarks must be used within a BookmarkProvider')
  }
  return context
}

interface BookmarkProviderProps {
  children: ReactNode
}

export const BookmarkProvider: React.FC<BookmarkProviderProps> = ({ children }) => {
  const [bookmarkData, setBookmarkData] = useState<BookmarkData>({
    bookmarks: [],
    collections: [],
    tags: [],
    playlists: []
  })
  const [searchQuery, setSearchQuery] = useState('')
  const [searchOptions, setSearchOptions] = useState<SearchOptions>({
    caseSensitive: false,
    exactMatch: false,
    searchFields: ['title', 'description', 'tags', 'url'],
    fuzzy: false,
    threshold: 0.6
  })
  const [selectedCollection, setSelectedCollection] = useState('all')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [selectedPlaylist, setSelectedPlaylist] = useState<string | null>(null)
  const [filterType, setFilterType] = useState<FilterType>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [isSelectMode, setIsSelectMode] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(() => {
    // Load auto-save preference from localStorage
    const saved = localStorage.getItem('bookmark-manager-autosave')
    return saved ? JSON.parse(saved) : false // DISABLED for memory optimization by default
  })
  const [attachedFile, setAttachedFile] = useState<{ name: string, lastModified: number } | null>(null)
  const [saveMode, setSaveMode] = useState<'update' | 'new'>('new')
  const [isUsingMockData, setIsUsingMockData] = useState(false)

  // Refs for auto-save functionality
  const autoSaveTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)
  const lastSaveHashRef = React.useRef<string>('') // Use hash instead of full data string
  const memoryCleanupIntervalRef = React.useRef<NodeJS.Timeout | null>(null)

  // Mock bookmarks for default display
  const getMockBookmarks = () => {
    const mockBookmarks = [
      // NEW: First new mock bookmark - moved to front with high visits
      {
        id: 'mock-new-1',
        title: 'Claude AI Assistant',
        url: 'https://claude.ai',
        description: 'Anthropic\'s AI assistant for conversations, analysis, and creative tasks - Just added!',
        summary: 'AI Assistant Platform: Advanced conversational AI by Anthropic designed for complex reasoning, analysis, and creative tasks. Features include document analysis, code generation, research assistance, and multi-turn conversations with strong safety measures.',
        tags: ['ai', 'assistant', 'anthropic', 'new'],
        collection: 'Quick Add',
        dateAdded: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
        isFavorite: true,
        visits: 999, // High visit count to appear at front
        favicon: 'https://www.google.com/s2/favicons?domain=claude.ai&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: true, // Show "NEW" indicator on Claude mock bookmark
        addedTimestamp: new Date(Date.now() - 1800000).toISOString(), // Track as recently added
        canRevert: true // Allow revert on Claude mock bookmark
      },
      // NEW: Second new mock bookmark - moved to front with high visits
      {
        id: 'mock-new-2',
        title: 'Perplexity AI',
        url: 'https://perplexity.ai',
        description: 'AI-powered search engine with real-time information - Freshly bookmarked!',
        summary: 'AI Search Engine: Real-time search platform that combines web search with AI analysis to provide comprehensive answers with source citations. Ideal for research, fact-checking, and getting current information on any topic.',
        tags: ['ai', 'search', 'research', 'new'],
        collection: 'Quick Add',
        dateAdded: new Date(Date.now() - 900000).toISOString(), // 15 minutes ago
        isFavorite: false,
        visits: 888, // High visit count to appear at front (second highest)
        favicon: 'https://www.google.com/s2/favicons?domain=perplexity.ai&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: true,
        addedTimestamp: new Date(Date.now() - 900000).toISOString(),
        canRevert: true
      },
      // ChatGPT bookmark - no longer showing as recently added
      {
        id: 'mock-recent-1',
        title: 'OpenAI ChatGPT',
        url: 'https://chat.openai.com',
        description: 'AI-powered conversational assistant',
        summary: 'AI Assistant Platform: OpenAI\'s flagship conversational AI for general-purpose tasks including writing, coding, analysis, and problem-solving. Features GPT-4 technology with web browsing, image analysis, and plugin capabilities.',
        tags: ['ai', 'assistant', 'productivity'],
        collection: 'Quick Add',
        dateAdded: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        isFavorite: false,
        visits: 1,
        favicon: 'https://www.google.com/s2/favicons?domain=chat.openai.com&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: false // Removed new indicator
      },
      {
        id: 'mock-1',
        title: 'GitHub',
        url: 'https://github.com',
        description: 'The world\'s leading software development platform',
        summary: 'Development Platform: Cloud-based Git repository hosting with collaboration tools, issue tracking, CI/CD pipelines, and project management. Essential for open-source development, team collaboration, and code version control.',
        tags: ['development', 'code', 'git'],
        collection: 'Development',
        dateAdded: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        isFavorite: true,
        visits: 42,
        favicon: 'https://www.google.com/s2/favicons?domain=github.com&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: false // Not recently added
      },
      {
        id: 'mock-2',
        title: 'Stack Overflow',
        url: 'https://stackoverflow.com',
        description: 'Where developers learn, share, & build careers',
        summary: 'Developer Community: Q&A platform for programming questions with expert answers, code examples, and solutions. Essential resource for debugging, learning new technologies, and staying current with development best practices.',
        tags: ['development', 'help', 'community'],
        collection: 'Development',
        dateAdded: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        isFavorite: false,
        visits: 28,
        favicon: 'https://www.google.com/s2/favicons?domain=stackoverflow.com&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: false // Not recently added
      },
      {
        id: 'mock-3',
        title: 'MDN Web Docs',
        url: 'https://developer.mozilla.org',
        description: 'Resources for developers, by developers',
        summary: 'Technical Documentation: Comprehensive web development reference covering HTML, CSS, JavaScript, and Web APIs. Includes tutorials, guides, and detailed specifications for building modern web applications.',
        tags: ['documentation', 'web', 'reference'],
        collection: 'Reference & Documentation',
        dateAdded: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
        isFavorite: true,
        visits: 35,
        favicon: 'https://www.google.com/s2/favicons?domain=developer.mozilla.org&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: false // Not recently added
      },
      {
        id: 'mock-4',
        title: 'React Documentation',
        url: 'https://react.dev',
        description: 'The library for web and native user interfaces',
        summary: 'Framework Documentation: Official React library documentation with component guides, hooks reference, and best practices. Learn to build interactive UIs with modern React patterns and performance optimization techniques.',
        tags: ['react', 'documentation', 'frontend'],
        collection: 'Reference & Documentation',
        dateAdded: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
        isFavorite: false,
        visits: 19,
        favicon: 'https://www.google.com/s2/favicons?domain=react.dev&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: false // Not recently added
      },
      {
        id: 'mock-5',
        title: 'TypeScript Handbook',
        url: 'https://www.typescriptlang.org/docs/',
        description: 'TypeScript extends JavaScript by adding types',
        summary: 'Programming Language Guide: Complete TypeScript documentation covering type system, advanced features, and migration strategies. Essential for building type-safe JavaScript applications with better tooling and error prevention.',
        tags: ['typescript', 'documentation', 'javascript'],
        collection: 'Reference & Documentation',
        dateAdded: new Date(Date.now() - 432000000).toISOString(), // 5 days ago
        isFavorite: false,
        visits: 23,
        favicon: 'https://www.google.com/s2/favicons?domain=typescriptlang.org&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: false // Not recently added
      },
      {
        id: 'mock-6',
        title: 'Figma',
        url: 'https://figma.com',
        description: 'The collaborative interface design tool',
        summary: 'Design Tool: Cloud-based interface design platform for creating UI/UX designs, prototypes, and design systems. Features real-time collaboration, component libraries, and developer handoff tools for seamless design-to-code workflow.',
        tags: ['design', 'ui', 'collaboration'],
        collection: 'Design & Creative',
        dateAdded: new Date(Date.now() - 518400000).toISOString(), // 6 days ago
        isFavorite: true,
        visits: 31,
        favicon: 'https://www.google.com/s2/favicons?domain=figma.com&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: false // Not recently added
      },
      // Vercel bookmark - no longer showing as recently added
      {
        id: 'mock-recent-2',
        title: 'Vercel',
        url: 'https://vercel.com',
        description: 'Deploy web projects with zero configuration',
        summary: 'Deployment Platform: Zero-configuration hosting for frontend frameworks with automatic deployments, edge functions, and global CDN. Optimized for React, Next.js, and modern web applications with instant previews.',
        tags: ['deployment', 'hosting', 'frontend'],
        collection: 'Quick Add',
        dateAdded: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        isFavorite: false,
        visits: 1,
        favicon: 'https://www.google.com/s2/favicons?domain=vercel.com&sz=32',
        selected: false,
        playlists: [],
        isRecentlyAdded: false // Removed new indicator
      },

      // AI & Machine Learning Collection
      {
        id: 'mock-ai-1',
        title: 'Hugging Face',
        url: 'https://huggingface.co',
        description: 'The AI community building the future. Platform for machine learning models, datasets, and applications.',
        summary: 'AI Model Hub: Open-source platform hosting thousands of pre-trained models, datasets, and ML applications. Access state-of-the-art transformers, computer vision models, and NLP tools with easy integration and deployment options.',
        tags: ['ai', 'machine-learning', 'models', 'datasets', 'community'],
        collection: 'AI & Machine Learning',
        dateAdded: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        isFavorite: true,
        visits: 23,
        favicon: 'https://www.google.com/s2/favicons?domain=huggingface.co&sz=32',
        selected: false,
        playlists: []
      },
      {
        id: 'mock-ai-2',
        title: 'OpenAI Platform',
        url: 'https://platform.openai.com',
        description: 'OpenAI API platform for building AI-powered applications with GPT models.',
        summary: 'AI API Platform: Developer platform for integrating GPT models into applications with comprehensive APIs, usage analytics, and fine-tuning capabilities. Build chatbots, content generation, and AI-powered features at scale.',
        tags: ['ai', 'api', 'gpt', 'development', 'chatgpt'],
        collection: 'AI & Machine Learning',
        dateAdded: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        isFavorite: false,
        visits: 15,
        favicon: 'https://www.google.com/s2/favicons?domain=platform.openai.com&sz=32',
        selected: false,
        playlists: []
      },

      // Productivity Collection
      {
        id: 'mock-prod-1',
        title: 'Notion',
        url: 'https://notion.so',
        description: 'All-in-one workspace for notes, docs, wikis, and project management.',
        summary: 'Productivity Platform: All-in-one workspace combining notes, databases, wikis, and project management. Create custom workflows, knowledge bases, and collaborative documents with powerful blocks and templates.',
        tags: ['productivity', 'notes', 'collaboration', 'workspace', 'organization'],
        collection: 'Productivity',
        dateAdded: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
        isFavorite: true,
        visits: 67,
        favicon: 'https://www.google.com/s2/favicons?domain=notion.so&sz=32',
        selected: false,
        playlists: []
      },
      {
        id: 'mock-prod-2',
        title: 'Todoist',
        url: 'https://todoist.com',
        description: 'Task management app that helps you organize work and life.',
        summary: 'Task Management Tool: Intelligent task organizer with natural language processing, project templates, and productivity tracking. Features include due dates, labels, filters, and team collaboration for personal and professional workflow optimization.',
        tags: ['productivity', 'tasks', 'organization', 'time-management', 'workflow'],
        collection: 'Productivity',
        dateAdded: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
        isFavorite: false,
        visits: 34,
        favicon: 'https://www.google.com/s2/favicons?domain=todoist.com&sz=32',
        selected: false,
        playlists: []
      },

      // Design & Creative Collection
      {
        id: 'mock-design-1',
        title: 'Dribbble',
        url: 'https://dribbble.com',
        description: 'Discover the world\'s top designers & creatives. Design inspiration and portfolio showcase.',
        summary: 'Design Community: Creative showcase platform featuring top designers\' work, UI/UX inspiration, and design trends. Discover innovative designs, connect with creative professionals, and showcase your portfolio to a global audience.',
        tags: ['design', 'inspiration', 'portfolio', 'creative', 'ui', 'graphics'],
        collection: 'Design & Creative',
        dateAdded: new Date(Date.now() - 432000000).toISOString(), // 5 days ago
        isFavorite: true,
        visits: 45,
        favicon: 'https://www.google.com/s2/favicons?domain=dribbble.com&sz=32',
        selected: false,
        playlists: []
      },
      {
        id: 'mock-design-2',
        title: 'Figma',
        url: 'https://figma.com',
        description: 'Collaborative interface design tool. Design, prototype, and gather feedback all in one place.',
        tags: ['design', 'ui', 'ux', 'prototyping', 'collaboration', 'figma'],
        collection: 'Design & Creative',
        dateAdded: new Date(Date.now() - 518400000).toISOString(), // 6 days ago
        isFavorite: true,
        visits: 89,
        favicon: 'https://www.google.com/s2/favicons?domain=figma.com&sz=32',
        selected: false,
        playlists: []
      },

      // Learning & Education Collection
      {
        id: 'mock-learn-1',
        title: 'Coursera',
        url: 'https://coursera.org',
        description: 'Online courses and degrees from top universities and companies worldwide.',
        summary: 'Online Learning Platform: University-level courses and professional certificates from top institutions like Stanford, Yale, and Google. Earn verified credentials in technology, business, data science, and more with flexible scheduling.',
        tags: ['education', 'courses', 'certification', 'online-learning', 'skills'],
        collection: 'Learning & Education',
        dateAdded: new Date(Date.now() - 604800000).toISOString(), // 7 days ago
        isFavorite: false,
        visits: 12,
        favicon: 'https://www.google.com/s2/favicons?domain=coursera.org&sz=32',
        selected: false,
        playlists: []
      },
      {
        id: 'mock-learn-2',
        title: 'Khan Academy',
        url: 'https://khanacademy.org',
        description: 'Free online courses, lessons and practice for learners of all ages.',
        summary: 'Educational Resource: Free comprehensive learning platform covering math, science, programming, history, and more. Interactive exercises, instructional videos, and personalized learning dashboard make complex subjects accessible to all ages.',
        tags: ['education', 'free', 'courses', 'learning', 'tutorials', 'academy'],
        collection: 'Learning & Education',
        dateAdded: new Date(Date.now() - 691200000).toISOString(), // 8 days ago
        isFavorite: true,
        visits: 28,
        favicon: 'https://www.google.com/s2/favicons?domain=khanacademy.org&sz=32',
        selected: false,
        playlists: []
      },
      {
        id: 'mock-learn-3',
        title: 'React Tutorial for Beginners - Full Course',
        url: 'https://www.youtube.com/watch?v=bMknfKXIFA8',
        description: 'Complete React tutorial covering components, hooks, state management, and modern patterns.',
        summary: 'Video Tutorial: Comprehensive React development course covering fundamentals to advanced concepts. Learn component architecture, hooks, state management, and modern React patterns through hands-on projects and real-world examples.',
        tags: ['react', 'tutorial', 'javascript', 'frontend', 'video', 'youtube'],
        collection: 'Learning & Education',
        dateAdded: new Date(Date.now() - 777600000).toISOString(), // 9 days ago
        isFavorite: false,
        visits: 8,
        favicon: 'https://www.google.com/s2/favicons?domain=youtube.com&sz=32',
        selected: false,
        playlists: []
      },
      {
        id: 'mock-test-no-summary',
        title: 'Test Bookmark - No Summary',
        url: 'https://www.wikipedia.org',
        description: 'This bookmark has no summary field to test the Generate Summaries button.',
        tags: ['test', 'no-summary'],
        collection: 'Quick Add',
        dateAdded: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        isFavorite: false,
        visits: 1,
        favicon: 'https://www.google.com/s2/favicons?domain=wikipedia.org&sz=32',
        selected: false,
        playlists: []
      },

      // News & Media Collection
      {
        id: 'mock-news-1',
        title: 'TechCrunch',
        url: 'https://techcrunch.com',
        description: 'Startup and technology news. Covering the latest in tech innovation and business.',
        summary: 'Tech News Source: Leading technology journalism covering startup funding, product launches, industry analysis, and emerging tech trends. Essential reading for entrepreneurs, investors, and tech professionals staying current with innovation.',
        tags: ['news', 'tech-news', 'startup', 'technology', 'business', 'innovation'],
        collection: 'News & Media',
        dateAdded: new Date(Date.now() - 777600000).toISOString(), // 9 days ago
        isFavorite: false,
        visits: 56,
        favicon: 'https://www.google.com/s2/favicons?domain=techcrunch.com&sz=32',
        selected: false,
        playlists: []
      },

      // Entertainment Collection
      {
        id: 'mock-ent-1',
        title: 'Spotify',
        url: 'https://spotify.com',
        description: 'Music streaming service with millions of songs, podcasts, and playlists.',
        tags: ['entertainment', 'music', 'streaming', 'podcasts', 'playlists'],
        collection: 'Entertainment',
        dateAdded: new Date(Date.now() - 864000000).toISOString(), // 10 days ago
        isFavorite: true,
        visits: 134,
        favicon: 'https://www.google.com/s2/favicons?domain=spotify.com&sz=32',
        selected: false,
        playlists: []
      },
      {
        id: 'mock-ent-2',
        title: 'Netflix',
        url: 'https://netflix.com',
        description: 'Streaming service for TV shows, movies, and original content.',
        summary: 'Streaming Platform: Global entertainment service offering unlimited access to movies, TV series, documentaries, and award-winning original content. Features personalized recommendations, offline downloads, and seamless streaming across all devices with no ads.',
        tags: ['entertainment', 'streaming', 'movies', 'tv-shows', 'netflix'],
        collection: 'Entertainment',
        dateAdded: new Date(Date.now() - 950400000).toISOString(), // 11 days ago
        isFavorite: true,
        visits: 78,
        favicon: 'https://www.google.com/s2/favicons?domain=netflix.com&sz=32',
        selected: false,
        playlists: []
      }
    ]

    // Enhanced collection system with diverse, practical categories
    const mockCollections = [
      { id: 'quick-add', name: 'Quick Add', color: '#10b981', count: 4 }, // 2 new + 2 regular bookmarks in Quick Add
      { id: 'development', name: 'Development', color: '#3b82f6', count: 2 },
      { id: 'ai-machine-learning', name: 'AI & Machine Learning', color: '#8b5cf6', count: 2 },
      { id: 'productivity', name: 'Productivity', color: '#f59e0b', count: 2 },
      { id: 'design-creative', name: 'Design & Creative', color: '#ec4899', count: 3 }, // Now includes all design bookmarks
      { id: 'learning-education', name: 'Learning & Education', color: '#06b6d4', count: 2 },
      { id: 'news-media', name: 'News & Media', color: '#ef4444', count: 1 },
      { id: 'entertainment', name: 'Entertainment', color: '#84cc16', count: 2 },
      { id: 'reference', name: 'Reference & Documentation', color: '#4f46e5', count: 3 }, // Original reference bookmarks
      // Additional collections for future expansion
      { id: 'finance', name: 'Finance & Business', color: '#22c55e', count: 0 },
      { id: 'health-fitness', name: 'Health & Fitness', color: '#14b8a6', count: 0 },
      { id: 'travel', name: 'Travel & Places', color: '#f97316', count: 0 },
      { id: 'shopping', name: 'Shopping', color: '#a855f7', count: 0 },
      { id: 'social', name: 'Social Networks', color: '#3b82f6', count: 0 },
      { id: 'gaming', name: 'Gaming', color: '#dc2626', count: 0 },
      { id: 'food-recipes', name: 'Food & Recipes', color: '#ea580c', count: 0 },
      { id: 'tech-news', name: 'Tech News', color: '#0891b2', count: 0 },
      { id: 'career', name: 'Career & Jobs', color: '#7c3aed', count: 0 },
      { id: 'personal', name: 'Personal', color: '#be185d', count: 0 },
      { id: 'research', name: 'Research & Science', color: '#059669', count: 0 }
    ]

    // Enhanced tag system to match the diverse collections
    const mockTags = [
      // AI & Tech
      'ai', 'machine-learning', 'chatgpt', 'claude', 'automation', 'neural-networks',
      // Development
      'development', 'code', 'programming', 'git', 'react', 'typescript', 'javascript', 'python', 'nodejs', 'api',
      // Design & Creative
      'design', 'ui', 'ux', 'figma', 'photoshop', 'illustration', 'branding', 'typography', 'color-theory',
      // Productivity
      'productivity', 'tools', 'workflow', 'automation', 'organization', 'time-management', 'collaboration',
      // Learning
      'education', 'courses', 'tutorials', 'certification', 'skills', 'online-learning', 'mooc',
      // Business & Finance
      'business', 'finance', 'investing', 'cryptocurrency', 'stocks', 'entrepreneurship', 'startup',
      // Health & Lifestyle
      'health', 'fitness', 'nutrition', 'wellness', 'mental-health', 'exercise', 'meditation',
      // Entertainment
      'entertainment', 'movies', 'music', 'gaming', 'streaming', 'podcasts', 'youtube',
      // News & Media
      'news', 'journalism', 'media', 'politics', 'current-events', 'analysis',
      // Travel & Places
      'travel', 'destinations', 'hotels', 'flights', 'culture', 'photography', 'adventure',
      // Shopping & Commerce
      'shopping', 'ecommerce', 'deals', 'reviews', 'marketplace', 'fashion', 'electronics',
      // Social & Communication
      'social', 'networking', 'communication', 'community', 'forums', 'messaging',
      // Reference & Documentation
      'reference', 'documentation', 'wiki', 'guides', 'tutorials', 'help', 'support',
      // Career & Professional
      'career', 'jobs', 'resume', 'interview', 'professional-development', 'networking',
      // Food & Cooking
      'food', 'recipes', 'cooking', 'restaurants', 'cuisine', 'nutrition', 'baking',
      // Research & Science
      'research', 'science', 'academic', 'papers', 'studies', 'data', 'analysis'
    ]

    // Don't create automatic Favorites playlist - use the main Favorites collection instead
    const mockPlaylists: Playlist[] = []

    return {
      bookmarks: mockBookmarks,
      collections: mockCollections,
      tags: mockTags,
      playlists: mockPlaylists
    }
  }



  // Function to reset to mock data (useful for testing and demos)
  const resetToMockData = () => {
    console.log('🔄 Resetting to mock data...')
    localStorage.removeItem('bookmarkData') // Clear any saved data
    const mockData = getMockBookmarks()
    setBookmarkData(mockData)
    setIsUsingMockData(true)
    setHasUnsavedChanges(false)
    console.log('✨ Reset to mock data complete - showing drag & drop examples')
    console.log('📋 Note: App will always start with mock data on browser/tab restart')
  }

  // Load initial data - CHECK FOR SAVED DATA FIRST, then fallback to mock
  useEffect(() => {
    console.log('🔄 useEffect triggered - isLoading:', isLoading, 'bookmarks:', bookmarkData.bookmarks.length)

    const loadBookmarks = async () => {
      console.log('🚀 loadBookmarks function called')
      try {
        // First, check if there's saved data from previous imports
        const savedData = localStorage.getItem('bookmarkData')
        console.log('📂 localStorage check result:', savedData ? 'Found data' : 'No data')

        if (savedData) {
          console.log('📂 Found saved bookmark data, loading...')
          const parsedData = JSON.parse(savedData)
          console.log('📂 Parsed data:', { bookmarks: parsedData.bookmarks?.length, collections: parsedData.collections?.length })

          // Validate and optimize the saved data
          const optimizedData = {
            bookmarks: (parsedData.bookmarks || []).map((bookmark: any) => ({
              id: bookmark.id,
              title: bookmark.title || 'Untitled',
              url: bookmark.url || '',
              description: bookmark.description || '',
              tags: bookmark.tags || [],
              collection: bookmark.collection || 'General',
              isFavorite: bookmark.isFavorite || false,
              dateAdded: bookmark.dateAdded || new Date().toISOString(),
              addedTimestamp: bookmark.addedTimestamp || new Date().toISOString(),
              isRecentlyAdded: bookmark.isRecentlyAdded || false,
              canRevert: bookmark.canRevert || false,
              selected: false,
              playlists: bookmark.playlists || []
            })),
            collections: parsedData.collections || [],
            tags: parsedData.tags || [],
            playlists: parsedData.playlists || []
          }

          console.log('📂 About to setBookmarkData with optimized data:', optimizedData)
          setBookmarkData(optimizedData)
          setIsUsingMockData(false)
          setIsLoading(false)
          console.log(`✅ Loaded ${optimizedData.bookmarks.length} bookmarks from saved data`)
          console.log(`📁 Collections: ${optimizedData.collections.length}`)
          console.log(`🏷️ Tags: ${optimizedData.tags.length}`)
          console.log('📂 setBookmarkData called successfully')
        } else {
          // No saved data, load mock data for demo
          console.log('📂 No saved data found, loading mock bookmarks for demo...')
          const mockData = getMockBookmarks()
          console.log('📂 Mock data generated:', { bookmarks: mockData.bookmarks?.length, collections: mockData.collections?.length })
          console.log('📂 About to setBookmarkData with mock data')
          setBookmarkData(mockData)
          setIsUsingMockData(true)
          setIsLoading(false)
          console.log('✨ Mock data loaded - import bookmarks to replace with real data')
          console.log('📂 setBookmarkData called successfully with mock data')
          console.warn('🎉 CONTEXT READY:', 'bookmarks=' + mockData.bookmarks.length, 'collections=' + mockData.collections.length)
        }
      } catch (error) {
        console.error('Error loading bookmarks:', error)
        console.log('📂 Error occurred, loading mock bookmarks as fallback')
        const mockData = getMockBookmarks()
        setBookmarkData(mockData)
        setIsUsingMockData(true)
      } finally {
        setIsLoading(false)
      }
    }

    // Add testing functions to window
    if (typeof window !== 'undefined') {
      (window as unknown as Window & { resetToMockData: () => void; testSmartAI: () => Promise<void> }).resetToMockData = resetToMockData;

      (window as unknown as Window & { testSmartAI: () => Promise<void> }).testSmartAI = async () => {
        console.log('🧪 Testing Smart AI organization...')
        console.log('🧪 Current collections count:', bookmarkData.collections.length)
        console.log('🧪 Current bookmarks count:', bookmarkData.bookmarks.length)

        const result = await autoOrganizeBookmarks({
          strategy: 'smart',
          preserveExistingFolders: false,
          useAI: true
        })

        console.log('🧪 Smart AI test result:', result)
        setTimeout(() => {
          console.log('🧪 New collections count after timeout:', bookmarkData.collections.length)
        }, 1000);
      };

      console.log('🛠️ Added resetToMockData() and testSmartAI() to window for testing')
    }

    loadBookmarks()
  }, []) // Empty dependency array - run once on mount
  // eslint-disable-next-line react-hooks/exhaustive-deps

  // Auto-save on page unload/refresh (optimized to prevent recreation)
  const bookmarkDataRef = React.useRef(bookmarkData)
  const hasUnsavedChangesRef = React.useRef(hasUnsavedChanges)

  // Update refs when values change
  React.useEffect(() => {
    bookmarkDataRef.current = bookmarkData
  }, [bookmarkData])

  React.useEffect(() => {
    hasUnsavedChangesRef.current = hasUnsavedChanges
  }, [hasUnsavedChanges])

  // Persist auto-save preference to localStorage
  React.useEffect(() => {
    localStorage.setItem('bookmark-manager-autosave', JSON.stringify(autoSaveEnabled))
  }, [autoSaveEnabled])

  // Set up beforeunload listener only once
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // DISABLED: Auto-save on unload to ensure fresh mock data on restart
      // This ensures we always start with mock data for better demo experience
      console.log('🔄 Page unloading - will start fresh with mock data on next load')

      // Show warning if there are unsaved changes for HTML export
      if (hasUnsavedChangesRef.current) {
        event.preventDefault()
        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return event.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, []) // Empty dependency array - only set up once

  // DISABLED: Auto-cleanup interval was causing memory issues

  // Cleanup auto-save timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
    }
  }, [])

  // Advanced filtering with search utilities and memory optimization
  const filteredBookmarks = React.useMemo(() => {
    console.log('🔍 Filtering bookmarks - Total:', bookmarkData.bookmarks.length, 'Selected collection:', selectedCollection, 'Filter type:', filterType)

    // For large datasets, use optimized filtering with higher limit to show all results
    if (bookmarkData.bookmarks.length > 1000) {
      const result = optimizedFilter.filterBookmarks(bookmarkData.bookmarks, {
        searchQuery,
        selectedCollection,
        selectedTags,
        filterType,
        limit: bookmarkData.bookmarks.length // Show all results, no artificial limit
      })
      console.log('🔍 Optimized filtering result:', result.bookmarks.length, 'of', result.totalCount)
      return result.bookmarks
    }

    // Standard filtering for smaller datasets
    let filtered = bookmarkData.bookmarks

    // Apply search filter using advanced search utilities
    if (searchQuery) {
      filtered = searchBookmarks(filtered, searchQuery, searchOptions)
      console.log('🔍 After advanced search filter:', filtered.length)
    }

    // Apply collection filter (but skip if favorites or recent filter is active)
    if (selectedCollection && selectedCollection !== 'all' && filterType !== 'favorites' && filterType !== 'recent') {
      // Handle special collection filters
      if (selectedCollection === 'favorites') {
        filtered = filtered.filter(bookmark => bookmark.isFavorite)
        console.log('🔍 After favorites collection filter:', filtered.length)
      } else if (selectedCollection === 'recent') {
        filtered = filtered.filter(bookmark => bookmark.isRecentlyAdded)
        console.log('🔍 After recent collection filter:', filtered.length)
      } else {
        // Normal collection filtering
        const collectionLower = selectedCollection.toLowerCase()
        filtered = filtered.filter(bookmark =>
          bookmark.collection.toLowerCase() === collectionLower
        )
        console.log('🔍 After collection filter:', filtered.length)
      }
    }

    // Apply tag filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(bookmark => {
        // For large datasets, use Set intersection for better performance
        if (bookmark.tags.length > 10) {
          const bookmarkTagSet = new Set(bookmark.tags)
          return selectedTags.every(tag => bookmarkTagSet.has(tag))
        }
        return selectedTags.every(tag => bookmark.tags.includes(tag))
      })
    }

    // Apply playlist filter
    if (selectedPlaylist) {
      const playlist = bookmarkData.playlists.find(p => p.id === selectedPlaylist)
      if (playlist) {
        const bookmarkIds = new Set(playlist.bookmarkIds)
        filtered = filtered.filter(bookmark => bookmarkIds.has(bookmark.id))
      }
    }

    // Apply type filter and sort
    let finalFiltered
    switch (filterType) {
      case 'favorites':
        finalFiltered = filtered.filter(bookmark => bookmark.isFavorite)
        break
      case 'recent':
        finalFiltered = filtered
          .filter(bookmark => bookmark.isRecentlyAdded)
          .sort((a, b) => {
            // Sort by addedTimestamp (most recently added first)
            const timeA = a.addedTimestamp ? new Date(a.addedTimestamp).getTime() : 0
            const timeB = b.addedTimestamp ? new Date(b.addedTimestamp).getTime() : 0
            return timeB - timeA
          })
        break
      default:
        finalFiltered = filtered.sort((a, b) => b.visits - a.visits)
    }

    console.log('🔍 Final filtered bookmarks:', finalFiltered.length, 'Filter type:', filterType)
    if (finalFiltered.length > 0) {
      console.log('🔍 First few bookmarks:', finalFiltered.slice(0, 3).map(b => ({ title: b.title, collection: b.collection, isRecentlyAdded: b.isRecentlyAdded })))
    }

    return finalFiltered
  }, [bookmarkData.bookmarks, bookmarkData.playlists, searchQuery, searchOptions, selectedCollection, selectedTags, selectedPlaylist, filterType])

  // Get selected bookmarks
  const selectedBookmarks = React.useMemo(() => {
    return bookmarkData.bookmarks.filter(bookmark => bookmark.selected)
  }, [bookmarkData.bookmarks])

  // Toggle bookmark favorite status
  const toggleBookmarkFavorite = (id: string) => {
    setBookmarkData(prev => {
      const bookmark = prev.bookmarks.find(b => b.id === id)
      if (!bookmark) return prev

      const newFavoriteStatus = !bookmark.isFavorite

      // Don't automatically create Favorites playlist - use main Favorites collection instead
      let updatedPlaylists = [...prev.playlists]

      // Update bookmarks
      const updatedBookmarks = prev.bookmarks.map(b => {
        if (b.id === id) {
          return {
            ...b,
            isFavorite: newFavoriteStatus
          }
        }
        return b
      })

      // No automatic playlist updates for favorites

      const updatedData = {
        ...prev,
        bookmarks: updatedBookmarks,
        playlists: updatedPlaylists
      }

      // Auto-save
      autoSave(updatedData)

      return updatedData
    })
  }

  // Update bookmark with partial data
  const updateBookmark = (id: string, updates: Partial<Bookmark>) => {
    setBookmarkData(prev => {
      const updatedData = {
        ...prev,
        bookmarks: prev.bookmarks.map(bookmark =>
          bookmark.id === id
            ? { ...bookmark, ...updates, updatedAt: new Date().toISOString() }
            : bookmark
        )
      }

      // Auto-save the updated bookmark
      autoSave(updatedData)
      return updatedData
    })

    // Mark as having unsaved changes for HTML export
    setHasUnsavedChanges(true)
    console.log(`📝 Bookmark updated: ${id}`, updates)
  }

  // Increment visit count when bookmark is clicked
  const incrementVisitCount = (id: string) => {
    setBookmarkData(prev => {
      const updatedData = {
        ...prev,
        bookmarks: prev.bookmarks.map(bookmark =>
          bookmark.id === id
            ? { ...bookmark, visits: bookmark.visits + 1, lastVisited: new Date().toISOString() }
            : bookmark
        )
      }

      // Auto-save the updated visit count
      autoSave(updatedData)
      return updatedData
    })

    // Mark as having unsaved changes for HTML export
    setHasUnsavedChanges(true)
    console.log(`📊 Visit count incremented for bookmark: ${id}`)
  }

  // Delete a bookmark
  const deleteBookmark = (id: string) => {
    setBookmarkData(prev => {
      // Get the bookmark to be deleted
      const bookmarkToDelete = prev.bookmarks.find(b => b.id === id)
      if (!bookmarkToDelete) return prev

      // Remove bookmark from any playlists
      const updatedPlaylists = prev.playlists.map(playlist => ({
        ...playlist,
        bookmarkIds: playlist.bookmarkIds.filter(bookmarkId => bookmarkId !== id)
      }))

      // Update the collection count
      const updatedCollections = prev.collections.map(collection =>
        collection.name === bookmarkToDelete.collection
          ? { ...collection, count: collection.count - 1 }
          : collection
      )

      const updatedData = {
        ...prev,
        bookmarks: prev.bookmarks.filter(bookmark => bookmark.id !== id),
        collections: updatedCollections,
        playlists: updatedPlaylists
      }

      // Auto-save
      autoSave(updatedData)

      return updatedData
    })

    // Mark as having unsaved changes for HTML export
    setHasUnsavedChanges(true)
    console.log('🗑️ Bookmark deleted and auto-saved to localStorage')
  }

  // Add a new bookmark
  const addBookmark = (newBookmark: Omit<Bookmark, 'id'>) => {
    const id = Date.now().toString()
    const currentDate = new Date().toISOString()

    // Analyze and assign intelligent collection if not already specified
    const analyzedCollection = newBookmark.collection || analyzeBookmarkTopic(newBookmark.url, newBookmark.title, newBookmark.description)

    const bookmark: Bookmark = {
      ...newBookmark,
      id,
      collection: analyzedCollection,
      selected: false,
      playlists: newBookmark.playlists || [],
      // PRESERVE DATES: Use original dateAdded if provided (from import), otherwise use current date
      dateAdded: newBookmark.dateAdded || currentDate,
      // PRESERVE TIMESTAMP: Use original addedTimestamp if provided (for imports), otherwise use current date
      addedTimestamp: newBookmark.addedTimestamp !== undefined ? newBookmark.addedTimestamp : currentDate
    }

    // Clear mock data flag when user adds real bookmarks
    setIsUsingMockData(false)

    setBookmarkData(prev => {
      // Update the collection count
      const updatedCollections = prev.collections.map(collection =>
        collection.name === bookmark.collection
          ? { ...collection, count: collection.count + 1 }
          : collection
      )

      const updatedData = {
        ...prev,
        bookmarks: [bookmark, ...prev.bookmarks], // Add to front
        collections: updatedCollections
      }

      // Auto-save now that we have real data
      autoSave(updatedData)

      return updatedData
    })
  }

  const addBookmarksBulk = (newBookmarks: Omit<Bookmark, 'id'>[]) => {
    console.log('🔄 addBookmarksBulk called with', newBookmarks.length, 'bookmarks')
    const currentDate = new Date().toISOString()

    const bookmarksWithIds = newBookmarks.map((bookmark, index) => ({
      ...bookmark,
      id: `${Date.now()}-${index}`,
      selected: false,
      playlists: bookmark.playlists || [],
      // PRESERVE DATES: Use original dateAdded if provided (from import), otherwise use current date
      dateAdded: bookmark.dateAdded || currentDate,
      // PRESERVE TIMESTAMP: Use original addedTimestamp if provided (for imports), otherwise use current date
      addedTimestamp: bookmark.addedTimestamp !== undefined ? bookmark.addedTimestamp : currentDate
    }))

    console.log('📋 Sample imported bookmarks:', bookmarksWithIds.slice(0, 3).map(b => ({ title: b.title, collection: b.collection, url: b.url.substring(0, 50) + '...' })))

    setBookmarkData(prev => {
      console.log('📊 Before bulk add - Total bookmarks:', prev.bookmarks.length, 'Collections:', prev.collections.length)

      // Update collections with new bookmarks
      const collectionsMap = new Map(prev.collections.map(c => [c.name, c.count]))

      bookmarksWithIds.forEach(bookmark => {
        const currentCount = collectionsMap.get(bookmark.collection) || 0
        collectionsMap.set(bookmark.collection, currentCount + 1)
      })

      const updatedCollections = Array.from(collectionsMap.entries()).map(([name, count]) => ({
        id: name.toLowerCase().replace(/\s+/g, '-'), // Generate ID from name
        name,
        count,
        color: getCollectionColor(name) // Get color for the collection
      }))

      const updatedData = {
        ...prev,
        bookmarks: [...bookmarksWithIds, ...prev.bookmarks], // Add to front
        collections: updatedCollections
      }

      console.log('✅ After bulk add - Total bookmarks:', updatedData.bookmarks.length, 'Collections:', updatedData.collections.length)
      console.log('📋 Updated collections:', updatedData.collections.map(c => `${c.name}: ${c.count}`))

      // Auto-save
      autoSave(updatedData)

      return updatedData
    })

    // Mark as having unsaved changes for HTML export
    setHasUnsavedChanges(true)
  }

  const clearAllBookmarks = () => {
    setBookmarkData(prev => ({
      ...prev,
      bookmarks: []
    }))
  }

  // Enhanced collection analysis system
  const analyzeBookmarkTopic = (url: string, title: string, description: string = ''): string => {
    const urlLower = url.toLowerCase()
    const titleLower = title.toLowerCase()
    const descLower = description.toLowerCase()
    const combinedText = `${urlLower} ${titleLower} ${descLower}`

    // Development & Programming
    if (combinedText.match(/(github|stackoverflow|codepen|jsfiddle|repl\.it|gitlab|bitbucket|dev\.to|programming|coding|developer|api|documentation|tutorial|framework|library|javascript|python|react|vue|angular|node|npm|yarn)/)) {
      return 'Development'
    }

    // Design & Creative
    if (combinedText.match(/(dribbble|behance|figma|sketch|adobe|photoshop|illustrator|design|ui|ux|creative|portfolio|inspiration|color|typography|mockup|wireframe)/)) {
      return 'Design'
    }

    // Learning & Education
    if (combinedText.match(/(coursera|udemy|khan|edx|pluralsight|lynda|skillshare|masterclass|course|tutorial|learn|education|study|university|college|training|certification)/)) {
      return 'Learning'
    }

    // News & Media
    if (combinedText.match(/(news|bbc|cnn|reuters|techcrunch|verge|wired|medium|blog|article|journalism|press|media|newspaper|magazine)/)) {
      return 'News'
    }

    // Entertainment & Media
    if (combinedText.match(/(youtube|netflix|spotify|twitch|hulu|disney|entertainment|movie|music|video|streaming|podcast|game|gaming|fun)/)) {
      return 'Entertainment'
    }

    // Social & Communication
    if (combinedText.match(/(facebook|twitter|instagram|linkedin|discord|slack|telegram|whatsapp|social|chat|messaging|community|forum|reddit)/)) {
      return 'Social'
    }

    // Shopping & E-commerce
    if (combinedText.match(/(amazon|ebay|etsy|shopify|store|shop|buy|purchase|cart|ecommerce|retail|marketplace|product|price)/)) {
      return 'Shopping'
    }

    // Tools & Productivity
    if (combinedText.match(/(notion|trello|asana|slack|zoom|calendar|productivity|tool|utility|app|software|service|workflow|automation)/)) {
      return 'Tools'
    }

    // Finance & Business
    if (combinedText.match(/(bank|finance|investment|stock|crypto|business|startup|entrepreneur|money|payment|accounting|tax)/)) {
      return 'Finance'
    }

    // Health & Fitness
    if (combinedText.match(/(health|fitness|medical|doctor|exercise|nutrition|wellness|diet|workout|yoga|mental|therapy)/)) {
      return 'Health'
    }

    // Travel & Lifestyle
    if (combinedText.match(/(travel|hotel|flight|booking|vacation|trip|lifestyle|food|recipe|cooking|restaurant)/)) {
      return 'Lifestyle'
    }

    // Reference & Documentation
    if (combinedText.match(/(wikipedia|reference|documentation|manual|guide|wiki|docs|help|support|faq)/)) {
      return 'Reference'
    }

    // Default fallback
    return 'General'
  }

  const replaceAllBookmarks = (newBookmarks: Omit<Bookmark, 'id'>[], fileInfo?: { name: string, lastModified: number }) => {
    // Clear mock data flag when importing real bookmarks
    setIsUsingMockData(false)

    const bookmarksWithIds = newBookmarks.map((bookmark, index) => {
      // Preserve original folder structure if it exists, otherwise use intelligent analysis
      let finalCollection: string

      if (bookmark.collection && bookmark.collection.trim() !== '') {
        // Use existing collection from imported file
        finalCollection = bookmark.collection
      } else if (bookmark.folder && bookmark.folder.trim() !== '') {
        // Use folder as collection if collection is not set
        finalCollection = bookmark.folder
      } else if (bookmark.path && bookmark.path.length > 0) {
        // Use the last folder in path as collection
        finalCollection = bookmark.path[bookmark.path.length - 1]
      } else {
        // Only use analyzed collection as fallback when no folder structure exists
        finalCollection = analyzeBookmarkTopic(bookmark.url, bookmark.title, bookmark.description)
      }

      return {
        ...bookmark,
        id: `${Date.now()}-${index}`,
        collection: finalCollection,
        selected: false,
        playlists: bookmark.playlists || []
      }
    })

    // Extract actual collections from imported bookmarks
    const collectionsMap = new Map<string, number>()

    // Check if bookmarks have real folder structure - ZERO FILTERING POLICY: Accept ALL collections
    const hasRealFolders = bookmarksWithIds.some(bookmark =>
      (bookmark.folder && bookmark.folder !== 'Imported') ||
      (bookmark.path && bookmark.path.length > 0) ||
      (bookmark.collection && bookmark.collection !== 'Imported')
      // REMOVED: Collection name filtering - preserve ALL collections exactly as they appear
    )



    if (hasRealFolders) {
      // Use actual folder structure from import
      bookmarksWithIds.forEach(bookmark => {
        const count = collectionsMap.get(bookmark.collection) || 0
        collectionsMap.set(bookmark.collection, count + 1)
      })
    } else {
      // No real folder structure found, use predefined collections with analyzed assignments
      const predefinedCollections = [
        'Development', 'Design', 'Learning', 'News', 'Entertainment',
        'Social', 'Shopping', 'Tools', 'Finance', 'Health',
        'Lifestyle', 'Reference', 'General'
      ]

      // Initialize all predefined collections
      predefinedCollections.forEach(collection => {
        collectionsMap.set(collection, 0)
      })

      // Count bookmarks in each collection
      bookmarksWithIds.forEach(bookmark => {
        const count = collectionsMap.get(bookmark.collection) || 0
        collectionsMap.set(bookmark.collection, count + 1)
      })
    }

    // Use centralized collection color system instead of hardcoded colors

    const newCollections: Collection[] = Array.from(collectionsMap.entries()).map(([name, count]) => ({
      id: name.toLowerCase().replace(/\s+/g, '-'),
      name,
      color: getCollectionColorForContext(name),
      count
    }))

    // Rebuild tags from new bookmarks
    const allTags = [...new Set(bookmarksWithIds.flatMap(bookmark => bookmark.tags))]

    const updatedData = {
      bookmarks: bookmarksWithIds,
      collections: newCollections,
      tags: allTags,
      playlists: [] // Clear playlists when replacing all bookmarks
    }

    setBookmarkData(updatedData)

    // Set attached file info if provided
    if (fileInfo) {
      setAttachedFile(fileInfo)
      setSaveMode('update') // Default to update mode when file is attached
      console.log(`📎 File attached: ${fileInfo.name}`)
    }

    // Auto-save
    autoSave(updatedData)

    // Mark as having unsaved changes for HTML export
    setHasUnsavedChanges(true)
  }

  // Detach the current file
  const detachFile = () => {
    setAttachedFile(null)
    setSaveMode('new')
    console.log('📎 File detached - will create new files on save')
  }

  // Use centralized collection color system instead of random colors
  const getCollectionColorForContext = (collectionName: string): string => {
    return getCollectionColor(collectionName)
  }

  // Toggle selection mode for split functionality
  const toggleSelectMode = () => {
    setIsSelectMode(prev => !prev)

    // If turning off select mode, deselect all bookmarks
    if (isSelectMode) {
      deselectAllBookmarks()
    }
  }

  // Toggle bookmark selection (for split functionality)
  const toggleBookmarkSelection = (id: string) => {
    console.log('🔄 Toggling selection for bookmark:', id)
    setBookmarkData(prev => {
      const bookmark = prev.bookmarks.find(b => b.id === id)
      console.log('📋 Current bookmark state:', bookmark?.selected ? 'selected' : 'not selected')

      const updated = {
        ...prev,
        bookmarks: prev.bookmarks.map(bookmark =>
          bookmark.id === id ? { ...bookmark, selected: !bookmark.selected } : bookmark
        )
      }

      const updatedBookmark = updated.bookmarks.find(b => b.id === id)
      console.log('✅ New bookmark state:', updatedBookmark?.selected ? 'selected' : 'not selected')

      return updated
    })
  }

  // Select all currently filtered bookmarks
  const selectAllBookmarks = () => {
    const filteredIds = new Set(filteredBookmarks.map(b => b.id))

    setBookmarkData(prev => ({
      ...prev,
      bookmarks: prev.bookmarks.map(bookmark =>
        filteredIds.has(bookmark.id) ? { ...bookmark, selected: true } : bookmark
      )
    }))
  }

  // Deselect all bookmarks
  const deselectAllBookmarks = () => {
    setBookmarkData(prev => ({
      ...prev,
      bookmarks: prev.bookmarks.map(bookmark => ({ ...bookmark, selected: false }))
    }))
  }

  // Bulk operations for selected bookmarks
  const deleteSelectedBookmarks = () => {
    const selectedIds = selectedBookmarks.map(bookmark => bookmark.id)

    if (selectedIds.length === 0) {
      console.log('⚠️ No bookmarks selected for deletion')
      return
    }

    console.log(`🗑️ Deleting ${selectedIds.length} selected bookmarks`)

    setBookmarkData(prev => {
      // Remove selected bookmarks from any playlists
      const updatedPlaylists = prev.playlists.map(playlist => ({
        ...playlist,
        bookmarkIds: playlist.bookmarkIds.filter(bookmarkId => !selectedIds.includes(bookmarkId))
      }))

      // Update collection counts
      const deletedBookmarks = prev.bookmarks.filter(b => selectedIds.includes(b.id))
      const collectionCounts = new Map<string, number>()

      deletedBookmarks.forEach(bookmark => {
        const count = collectionCounts.get(bookmark.collection) || 0
        collectionCounts.set(bookmark.collection, count + 1)
      })

      const updatedCollections = prev.collections.map(collection => {
        const deletedCount = collectionCounts.get(collection.name) || 0
        return {
          ...collection,
          count: Math.max(0, collection.count - deletedCount)
        }
      })

      const updatedData = {
        ...prev,
        bookmarks: prev.bookmarks.filter(bookmark => !selectedIds.includes(bookmark.id)),
        collections: updatedCollections,
        playlists: updatedPlaylists
      }

      // Auto-save
      autoSave(updatedData)

      return updatedData
    })

    // Mark as having unsaved changes for HTML export
    setHasUnsavedChanges(true)
    console.log(`✅ Deleted ${selectedIds.length} bookmarks and auto-saved`)
  }

  const addSelectedToPlaylist = (playlistId: string) => {
    const selectedIds = selectedBookmarks.map(bookmark => bookmark.id)

    if (selectedIds.length === 0) {
      console.log('⚠️ No bookmarks selected for playlist addition')
      return
    }

    console.log(`📝 Adding ${selectedIds.length} selected bookmarks to playlist`)

    setBookmarkData(prev => {
      // Update bookmarks to include playlist reference
      const updatedBookmarks = prev.bookmarks.map(bookmark => {
        if (selectedIds.includes(bookmark.id)) {
          const currentPlaylists = bookmark.playlists || []
          if (!currentPlaylists.includes(playlistId)) {
            return {
              ...bookmark,
              playlists: [...currentPlaylists, playlistId]
            }
          }
        }
        return bookmark
      })

      // Update playlist to include bookmark IDs
      const updatedPlaylists = prev.playlists.map(playlist => {
        if (playlist.id === playlistId) {
          const newBookmarkIds = selectedIds.filter(id => !playlist.bookmarkIds.includes(id))
          return {
            ...playlist,
            bookmarkIds: [...playlist.bookmarkIds, ...newBookmarkIds]
          }
        }
        return playlist
      })

      const updatedData = {
        ...prev,
        bookmarks: updatedBookmarks,
        playlists: updatedPlaylists
      }

      // Auto-save
      autoSave(updatedData)

      return updatedData
    })

    console.log(`✅ Added ${selectedIds.length} bookmarks to playlist`)
  }

  // Export bookmarks functionality
  const exportBookmarks = (format: ExportFormat, filename: string, bookmarksToExport?: Bookmark[]) => {
    const bookmarksToUse = bookmarksToExport || filteredBookmarks
    let content = ''
    let mimeType = ''

    switch (format) {
      case 'html':
        content = generateHtmlExport(bookmarksToUse)
        mimeType = 'text/html'
        if (!filename.endsWith('.html')) filename += '.html'
        break
      case 'json':
        content = generateJsonExport(bookmarksToUse)
        mimeType = 'application/json'
        if (!filename.endsWith('.json')) filename += '.json'
        break
      case 'csv':
        content = generateCsvExport(bookmarksToUse)
        mimeType = 'text/csv'
        if (!filename.endsWith('.csv')) filename += '.csv'
        break
    }

    // Create and trigger download
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Generate HTML export content
  const generateHtmlExport = (bookmarks: Bookmark[]): string => {
    let html = `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
`

    // Group bookmarks by collection
    const bookmarksByCollection: { [key: string]: Bookmark[] } = {}
    bookmarks.forEach(bookmark => {
      if (!bookmarksByCollection[bookmark.collection]) {
        bookmarksByCollection[bookmark.collection] = []
      }
      bookmarksByCollection[bookmark.collection].push(bookmark)
    })

    // Create HTML structure
    Object.keys(bookmarksByCollection).forEach(collection => {
      html += `    <DT><H3>${collection}</H3>\n    <DL><p>\n`

      bookmarksByCollection[collection].forEach(bookmark => {
        html += `        <DT><A HREF="${bookmark.url}" ${bookmark.tags.length ? `TAGS="${bookmark.tags.join(',')}"` : ''}>${bookmark.title}</A>\n`
        if (bookmark.description) {
          html += `        <DD>${bookmark.description}\n`
        }
      })

      html += `    </DL><p>\n`
    })

    html += `</DL><p>`
    return html
  }

  // Generate JSON export content
  const generateJsonExport = (bookmarks: Bookmark[]): string => {
    const exportData = {
      bookmarks: bookmarks.map(({ id: _id, selected: _selected, ...rest }) => rest), // Exclude internal fields
      collections: bookmarkData.collections.filter(collection =>
        bookmarks.some(bookmark => bookmark.collection === collection.name)
      ),
      tags: [...new Set(bookmarks.flatMap(bookmark => bookmark.tags))]
    }

    return JSON.stringify(exportData, null, 2)
  }

  // Generate CSV export content
  const generateCsvExport = (bookmarks: Bookmark[]): string => {
    const headers = 'Title,URL,Description,Tags,Collection,Favorite,Date Added,Visits\n'

    const rows = bookmarks.map(bookmark => {
      const title = bookmark.title.replace(/"/g, '""')
      const url = bookmark.url.replace(/"/g, '""')
      const description = bookmark.description.replace(/"/g, '""')
      const tags = `"${bookmark.tags.join(',')}"`
      const collection = bookmark.collection.replace(/"/g, '""')
      const favorite = bookmark.isFavorite
      const dateAdded = bookmark.dateAdded
      const visits = bookmark.visits

      return `"${title}","${url}","${description}",${tags},"${collection}",${favorite},"${dateAdded}",${visits}`
    }).join('\n')

    return headers + rows
  }

  // Split bookmarks functionality
  const splitBookmarks = (criteria: SplitCriteria, value?: string): { [key: string]: Bookmark[] } => {
    const bookmarksToSplit = selectedBookmarks.length > 0 ? selectedBookmarks : filteredBookmarks
    const result: { [key: string]: Bookmark[] } = {}

    switch (criteria) {
      case 'collection':
        // Group bookmarks by collection
        bookmarksToSplit.forEach(bookmark => {
          if (!result[bookmark.collection]) {
            result[bookmark.collection] = []
          }
          result[bookmark.collection].push(bookmark)
        })
        break

      case 'tag':
        // Group bookmarks by specified tag
        if (value) {
          result[value] = bookmarksToSplit.filter(bookmark =>
            bookmark.tags.includes(value)
          )
          result['without_' + value] = bookmarksToSplit.filter(bookmark =>
            !bookmark.tags.includes(value)
          )
        } else {
          // If no tag specified, create groups for each tag
          const allTags = [...new Set(bookmarksToSplit.flatMap(bookmark => bookmark.tags))]
          allTags.forEach(tag => {
            result[tag] = bookmarksToSplit.filter(bookmark =>
              bookmark.tags.includes(tag)
            )
          })
        }
        break

      case 'manual':
        // For manual selection, just return the selected bookmarks as one group
        result['selected'] = selectedBookmarks
        result['unselected'] = bookmarksToSplit.filter(bookmark => !bookmark.selected)
        break
    }

    return result
  }

  // Playlist functions
  const createPlaylist = (name: string, description: string, color: string, bookmarkIds: string[] = []) => {
    const id = `playlist_${Date.now()}`
    const newPlaylist: Playlist = {
      id,
      name,
      description,
      color,
      bookmarkIds,
      dateCreated: new Date().toISOString()
    }

    setBookmarkData(prev => ({
      ...prev,
      playlists: [...prev.playlists, newPlaylist]
    }))

    // Update bookmarks with the playlist reference
    if (bookmarkIds.length > 0) {
      setBookmarkData(prev => ({
        ...prev,
        bookmarks: prev.bookmarks.map(bookmark =>
          bookmarkIds.includes(bookmark.id)
            ? {
              ...bookmark,
              playlists: [...(bookmark.playlists || []), id]
            }
            : bookmark
        )
      }))
    }

    return id
  }

  const updatePlaylist = (id: string, updates: Partial<Omit<Playlist, 'id'>>) => {
    setBookmarkData(prev => {
      const playlistIndex = prev.playlists.findIndex(p => p.id === id)
      if (playlistIndex === -1) return prev

      const updatedPlaylists = [...prev.playlists]
      updatedPlaylists[playlistIndex] = {
        ...updatedPlaylists[playlistIndex],
        ...updates
      }

      return {
        ...prev,
        playlists: updatedPlaylists
      }
    })
  }

  const deletePlaylist = (id: string) => {
    setBookmarkData(prev => {
      // Remove playlist from bookmarks
      const updatedBookmarks = prev.bookmarks.map(bookmark => {
        if (bookmark.playlists?.includes(id)) {
          return {
            ...bookmark,
            playlists: bookmark.playlists.filter(playlistId => playlistId !== id)
          }
        }
        return bookmark
      })

      // Remove playlist
      return {
        ...prev,
        playlists: prev.playlists.filter(playlist => playlist.id !== id),
        bookmarks: updatedBookmarks
      }
    })
  }

  const addBookmarkToPlaylist = (bookmarkId: string, playlistId: string) => {
    setBookmarkData(prev => {
      // Add bookmark to playlist
      const updatedPlaylists = prev.playlists.map(playlist => {
        if (playlist.id === playlistId && !playlist.bookmarkIds.includes(bookmarkId)) {
          return {
            ...playlist,
            bookmarkIds: [...playlist.bookmarkIds, bookmarkId]
          }
        }
        return playlist
      })

      // Add playlist reference to bookmark
      const updatedBookmarks = prev.bookmarks.map(bookmark => {
        if (bookmark.id === bookmarkId) {
          const bookmarkPlaylists = bookmark.playlists || []
          if (!bookmarkPlaylists.includes(playlistId)) {
            return {
              ...bookmark,
              playlists: [...bookmarkPlaylists, playlistId]
            }
          }
        }
        return bookmark
      })

      return {
        ...prev,
        playlists: updatedPlaylists,
        bookmarks: updatedBookmarks
      }
    })
  }

  const removeBookmarkFromPlaylist = (bookmarkId: string, playlistId: string) => {
    setBookmarkData(prev => {
      // Remove bookmark from playlist
      const updatedPlaylists = prev.playlists.map(playlist => {
        if (playlist.id === playlistId) {
          return {
            ...playlist,
            bookmarkIds: playlist.bookmarkIds.filter(id => id !== bookmarkId)
          }
        }
        return playlist
      })

      // Remove playlist reference from bookmark
      const updatedBookmarks = prev.bookmarks.map(bookmark => {
        if (bookmark.id === bookmarkId && bookmark.playlists?.includes(playlistId)) {
          return {
            ...bookmark,
            playlists: bookmark.playlists.filter(id => id !== playlistId)
          }
        }
        return bookmark
      })

      return {
        ...prev,
        playlists: updatedPlaylists,
        bookmarks: updatedBookmarks
      }
    })
  }

  const getPlaylistBookmarks = (playlistId: string): Bookmark[] => {
    const playlist = bookmarkData.playlists.find(p => p.id === playlistId)
    if (!playlist) return []

    const bookmarkIds = new Set(playlist.bookmarkIds)
    return bookmarkData.bookmarks.filter(bookmark => bookmarkIds.has(bookmark.id))
  }

  // Drag & Drop functionality - OPTIMIZED for performance
  const addBookmarkFromDrop = async (newBookmark: Omit<Bookmark, 'id'>): Promise<string> => {
    const id = `drop_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const currentDate = new Date().toISOString()

    // Use provided collection or default to 'Quick Add' - defer analysis to background
    const quickCollection = newBookmark.collection || 'Quick Add'

    const bookmark: Bookmark = {
      ...newBookmark,
      id,
      collection: quickCollection,
      selected: false,
      playlists: newBookmark.playlists || [],
      // PRESERVE DATES: Use original dateAdded if provided, otherwise use current date
      dateAdded: newBookmark.dateAdded || currentDate,
      // PRESERVE TIMESTAMP: Use original addedTimestamp if provided, otherwise use current date
      addedTimestamp: newBookmark.addedTimestamp !== undefined ? newBookmark.addedTimestamp : currentDate
    }

    // Transition from mock data to real data when adding bookmarks
    setIsUsingMockData(false)

    console.log('🎯 Adding bookmark to context:', bookmark)

    // FAST: Immediate UI update with minimal processing
    setBookmarkData(prev => {
      console.log('🎯 Current bookmarks count:', prev.bookmarks.length)

      // Quick collection update - defer heavy processing
      const updatedCollections = prev.collections.map(collection =>
        collection.name === bookmark.collection
          ? { ...collection, count: collection.count + 1 }
          : collection
      )

      // Add new collection if it doesn't exist
      const collectionExists = prev.collections.some(c => c.name === bookmark.collection)
      if (!collectionExists) {
        console.log('🎯 Creating new collection:', bookmark.collection)
        updatedCollections.push({
          id: bookmark.collection.toLowerCase().replace(/\s+/g, '-'),
          name: bookmark.collection,
          color: getCollectionColorForContext(bookmark.collection),
          count: 1
        })
      }

      const updatedData = {
        ...prev,
        bookmarks: [bookmark, ...prev.bookmarks], // Add to beginning for visibility
        collections: updatedCollections
      }

      console.log('🎯 Updated bookmarks count:', updatedData.bookmarks.length)
      console.log('🎯 New bookmark added:', bookmark.title, 'Collection:', bookmark.collection)

      // BACKGROUND: Defer heavy operations to next tick
      setTimeout(() => {
        // Analyze topic in background if no collection was provided
        if (!newBookmark.collection) {
          const analyzedCollection = analyzeBookmarkTopic(newBookmark.url, newBookmark.title, newBookmark.description)

          // Update collection if analysis suggests a better one
          if (analyzedCollection !== 'Quick Add') {
            setBookmarkData(currentData => {
              const updatedBookmarks = currentData.bookmarks.map(b =>
                b.id === bookmark.id ? { ...b, collection: analyzedCollection } : b
              )

              const finalData = {
                ...currentData,
                bookmarks: updatedBookmarks
              }

              // Auto-save the analyzed version
              autoSave(finalData)
              return finalData
            })
          }
        }

        // Auto-save in background
        autoSave(updatedData)
      }, 0)

      return updatedData
    })

    // Mark as having unsaved changes for HTML export
    setHasUnsavedChanges(true)

    return id
  }

  const revertRecentBookmark = (id: string) => {
    setBookmarkData(prev => {
      const bookmarkToRemove = prev.bookmarks.find(b => b.id === id)
      if (!bookmarkToRemove) return prev

      // Update the collection count
      const updatedCollections = prev.collections.map(collection =>
        collection.name === bookmarkToRemove.collection
          ? { ...collection, count: Math.max(0, collection.count - 1) }
          : collection
      ).filter(collection => collection.count > 0) // Remove empty collections

      return {
        ...prev,
        bookmarks: prev.bookmarks.filter(bookmark => bookmark.id !== id),
        collections: updatedCollections
      }
    })

    // Mark as having unsaved changes
    setHasUnsavedChanges(true)
  }

  const clearRecentStatus = (id: string) => {
    setBookmarkData(prev => ({
      ...prev,
      bookmarks: prev.bookmarks.map(bookmark =>
        bookmark.id === id
          ? {
            ...bookmark,
            isRecentlyAdded: false,
            canRevert: false,
            addedTimestamp: undefined
          }
          : bookmark
      )
    }))
  }



  // Memory-optimized debounced auto-save function with intelligent storage management

  // Create a lightweight hash of the data for change detection
  const createDataHash = React.useCallback((data: BookmarkData): string => {
    const essentialData = {
      bookmarkCount: data.bookmarks.length,
      lastModified: data.bookmarks[0]?.dateAdded || '',
      collectionsCount: data.collections.length,
      tagsCount: data.tags.length
    }
    return JSON.stringify(essentialData)
  }, [])

  const autoSave = React.useCallback((data: BookmarkData): void => {
    if (!autoSaveEnabled || isUsingMockData) return // Don't save mock data

    // Check if autoSave is temporarily disabled (during summary generation)
    if (typeof window !== 'undefined' && (window as any).__DISABLE_AUTOSAVE__) {
      console.log('💾 AutoSave temporarily disabled during summary generation')
      return
    }

    // Clear existing timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current)
    }

    // Increased debounce during summary generation to reduce saves
    const debounceTime = data.bookmarks.some(b => b.summary && b.summary.includes('Generating...')) ? 5000 : 2000;

    autoSaveTimeoutRef.current = setTimeout(() => {
      try {
        // Use lightweight hash for change detection instead of full JSON comparison
        const currentHash = createDataHash(data)
        if (currentHash === lastSaveHashRef.current) {
          return // No significant changes, skip save
        }

        const isLargeDataset = data.bookmarks.length > 1500;

        // Offload to worker with error handling
        if (workerRef.current) {
          // Clear any existing message handler to prevent memory leaks
          workerRef.current.onmessage = null;

          workerRef.current.onmessage = (event) => {
            if (event.data.success) {
              localStorage.setItem('bookmarkData', event.data.data);
              lastSaveHashRef.current = currentHash;
              console.log('💾 Auto-saved to localStorage using Web Worker');
            } else {
              console.error('Worker stringification failed:', event.data.error);
              // Fallback to main thread save
              try {
                localStorage.setItem('bookmarkData', JSON.stringify(data));
                lastSaveHashRef.current = currentHash;
                console.log('💾 Auto-saved to localStorage (fallback)');
              } catch (fallbackError) {
                console.error('Fallback save failed:', fallbackError);
              }
            }
            // Clear the message handler after use
            if (workerRef.current) {
              workerRef.current.onmessage = null;
            }
          };

          workerRef.current.postMessage({
            bookmarks: data.bookmarks,
            collections: data.collections,
            tags: data.tags,
            playlists: data.playlists,
            isLargeDataset
          });
        }

        lastSaveHashRef.current = currentHash

        if (attachedFile && saveMode === 'update') {
          console.log(`💾 Auto-saved to localStorage (attached: ${attachedFile.name})`)
          setHasUnsavedChanges(true)
        } else {
          console.log('💾 Auto-saved to localStorage')
        }
      } catch (error) {
        console.error('Auto-save failed:', error)
        // If localStorage is full, try to clear some space
        if (error instanceof Error && error.name === 'QuotaExceededError') {
          console.warn('localStorage quota exceeded, attempting cleanup...')
          try {
            // Remove non-essential data and retry
            const essentialData = {
              bookmarks: data.bookmarks.map(({ selected: _selected, isRecentlyAdded: _isRecentlyAdded, canRevert: _canRevert, addedTimestamp: _addedTimestamp, ...bookmark }) => bookmark),
              collections: data.collections,
              tags: data.tags,
              playlists: data.playlists
            }
            localStorage.setItem('bookmarkData', JSON.stringify(essentialData))
            console.log('💾 Auto-saved with reduced data size')
          } catch (retryError) {
            console.error('Failed to save even reduced data:', retryError)
          }
        }
      }
    }, 1000)
  }, [autoSaveEnabled, attachedFile, saveMode, isUsingMockData, createDataHash])

  // DISABLED: Memory cleanup interval was causing high memory usage

  // Web Worker for offloading stringification
  const workerRef = React.useRef<Worker | null>(null);

  React.useEffect(() => {
    try {
      // Try to use the JavaScript version first (more compatible)
      workerRef.current = new Worker(new URL('../workers/stringifyWorker.js', import.meta.url));
      console.log('📦 Initialized stringify worker (JS version)');
    } catch (error) {
      console.error('Failed to initialize JS worker:', error);
      try {
        // Fall back to TypeScript version if needed
        workerRef.current = new Worker(new URL('../workers/stringifyWorker.ts', import.meta.url));
        console.log('📦 Initialized stringify worker (TS version)');
      } catch (fallbackError) {
        console.error('Failed to initialize worker:', fallbackError);
      }
    }

    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
      }
    };
  }, []);

  // Cleanup on unmount to prevent memory leaks
  React.useEffect(() => {
    return () => {
      // Clear all timeouts
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
      if (memoryCleanupIntervalRef.current) {
        clearInterval(memoryCleanupIntervalRef.current)
      }

      // Clear refs to prevent memory leaks
      lastSaveHashRef.current = ''

      console.log('🧹 BookmarkContext cleanup completed')
    }
  }, [])

  // Save bookmarks to HTML file and clear "new" status (localStorage auto-saves separately)
  const saveBookmarks = async (): Promise<void> => {
    console.log('💾 Saving bookmarks to localStorage and exporting to HTML...')
    try {
      // Clear all "recently added" status and get the updated data
      let updatedData: BookmarkData
      setBookmarkData(prev => {
        updatedData = {
          ...prev,
          bookmarks: prev.bookmarks.map(bookmark => ({
            ...bookmark,
            isRecentlyAdded: false,
            canRevert: false,
            addedTimestamp: undefined
          }))
        }
        return updatedData
      })

      // Actually persist to localStorage
      localStorage.setItem('bookmarkData', JSON.stringify(updatedData!))
      console.log('✅ Bookmarks saved to localStorage successfully!')

      // Auto-export to HTML file
      await exportBookmarksToHTML(updatedData!)

      // Mark as saved
      setHasUnsavedChanges(false)

      // Small delay for user feedback
      await new Promise(resolve => setTimeout(resolve, 300))

    } catch (error) {
      console.error('Error saving bookmarks:', error)
      throw error
    }
  }

  // Export bookmarks to HTML file
  const exportBookmarksToHTML = async (data: BookmarkData): Promise<void> => {
    try {
      let filename: string

      if (attachedFile && saveMode === 'update') {
        // Update the attached file
        filename = attachedFile.name.endsWith('.html') ? attachedFile.name : `${attachedFile.name}.html`
        console.log(`📁 Updating attached file: ${filename}`)
      } else {
        // Create new timestamped file
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
        filename = `bookmarks-saved-${timestamp}.html`
        console.log(`📁 Creating new bookmark file: ${filename}`)
      }

      console.log(`📁 Exporting ${data.bookmarks.length} bookmarks`)
      console.log('📁 Bookmark titles being exported:', data.bookmarks.map(b => b.title))

      // Generate HTML content
      const htmlContent = generateBookmarkHTML(data)

      // Create and download file
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      if (attachedFile && saveMode === 'update') {
        console.log(`✅ Updated ${filename} - replace your original file with this download`)
      } else {
        console.log(`✅ Bookmarks exported to ${filename}`)
        console.log(`💡 Use this new file for future imports to preserve all changes`)
      }
    } catch (error) {
      console.error('Error exporting bookmarks to HTML:', error)
    }
  }

  // Generate HTML bookmark file content
  const generateBookmarkHTML = (data: BookmarkData): string => {
    const timestamp = Math.floor(Date.now() / 1000)

    let html = `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
`

    // Group bookmarks by collection
    const bookmarksByCollection = data.bookmarks.reduce((acc, bookmark) => {
      const collection = bookmark.collection || 'Uncategorized'
      if (!acc[collection]) acc[collection] = []
      acc[collection].push(bookmark)
      return acc
    }, {} as Record<string, Bookmark[]>)

    // Generate HTML for each collection
    Object.entries(bookmarksByCollection).forEach(([collectionName, bookmarks]) => {
      html += `    <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">${collectionName}</H3>\n`
      html += `    <DL><p>\n`

      bookmarks.forEach(bookmark => {
        const addDate = bookmark.dateAdded ? Math.floor(new Date(bookmark.dateAdded).getTime() / 1000) : timestamp
        const tags = bookmark.tags.length > 0 ? ` TAGS="${bookmark.tags.join(',')}"` : ''
        const icon = bookmark.favicon ? ` ICON="${bookmark.favicon}"` : ''

        html += `        <DT><A HREF="${bookmark.url}" ADD_DATE="${addDate}" LAST_MODIFIED="${addDate}"${tags}${icon}>${bookmark.title}</A>\n`

        if (bookmark.description) {
          html += `        <DD>${bookmark.description}\n`
        }
      })

      html += `    </DL><p>\n`
    })

    html += `</DL><p>`

    return html
  }

  // Clear cache and refresh to initial state
  const clearCacheAndRefresh = (): void => {
    try {
      // Clear all localStorage data
      localStorage.removeItem('bookmarkData')
      localStorage.clear() // Clear everything to ensure clean state
      console.log('🧹 Cache cleared manually')

      // Clear all timeouts and intervals
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
        autoSaveTimeoutRef.current = null
      }

      // Reset all state to initial values with mock data
      const mockData = getMockBookmarks()
      setBookmarkData(mockData)
      setIsLoading(false)
      setIsSelectMode(false)
      setHasUnsavedChanges(false)
      setAutoSaveEnabled(true)
      setAttachedFile(null)
      setSaveMode('new')
      setSearchQuery('')
      setSelectedCollection('all')
      setSelectedTags([])
      setSelectedPlaylist(null)
      setFilterType('all')
      setIsUsingMockData(true) // Reset to mock data state

      console.log('🔄 Application state reset to initial values')

      // Force a hard reload to ensure clean state
      setTimeout(() => {
        window.location.reload()
      }, 100)

    } catch (error) {
      console.error('Error clearing cache:', error)
      // Fallback: force reload anyway
      window.location.reload()
    }
  }

  // Import bookmarks functionality (simplified version)
  const importBookmarks = async (fileContent: string, format: 'html' | 'json' | 'csv', _fileInfo?: { name: string, lastModified: number }, overwrite: boolean = false): Promise<{ success: boolean, count: number, message?: string }> => {
    try {
      // CRITICAL: Force garbage collection before processing
      if (typeof window !== 'undefined' && (window as Window & { gc?: () => void }).gc) {
        (window as Window & { gc: () => void }).gc()
      }

      let newBookmarks: Omit<Bookmark, 'id'>[] = []

      // Parse based on format (simplified parsing)
      if (format === 'json') {
        const data = JSON.parse(fileContent)
        newBookmarks = data.bookmarks || []

        // Clear the parsed data reference
        data.bookmarks = null
      }
      // Add HTML and CSV parsing as needed

      if (newBookmarks.length === 0) {
        return { success: false, count: 0, message: 'No valid bookmarks found in the file' }
      }

      // Update bookmark data
      if (overwrite) {
        replaceAllBookmarks(newBookmarks)
      } else {
        addBookmarksBulk(newBookmarks)
      }

      // CRITICAL: Clear the bookmarks array to free memory
      const count = newBookmarks.length
      newBookmarks.length = 0

      // Force final garbage collection
      if (typeof window !== 'undefined' && (window as Window & { gc?: () => void }).gc) {
        (window as Window & { gc: () => void }).gc()
      }

      return { success: true, count }
    } catch (error) {
      console.error('Error importing bookmarks:', error)

      // Force garbage collection even on error
      if (typeof window !== 'undefined' && (window as Window & { gc?: () => void }).gc) {
        (window as Window & { gc: () => void }).gc()
      }

      return { success: false, count: 0, message: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  // Auto-organize functionality
  const autoOrganizeBookmarks = async (options: { strategy: string, preserveExistingFolders: boolean, useAI: boolean }) => {
    try {
      // Force debug output that bypasses console protection
      const originalConsoleLog = console.log;
      const debugLog = (...args: unknown[]) => {
        originalConsoleLog('🔥 SMART AI DEBUG:', ...args);
        // Also try to show in UI if possible
        if (typeof window !== 'undefined') {
          (window as any).lastSmartAIDebug = args.join(' ');
        }
      };

      debugLog('🚀 AUTO-ORGANIZE STARTED:', options);
      debugLog('📊 Current state - Collections:', bookmarkData.collections.length, 'Bookmarks:', bookmarkData.bookmarks.length);

      // Import the service dynamically to avoid circular dependencies
      const { autoOrganizeService } = await import('../../services/autoOrganizeService')

      debugLog('📞 Calling autoOrganizeService.organizeBookmarks...');

      const result = await autoOrganizeService.organizeBookmarks(bookmarkData.bookmarks, {
        strategy: options.strategy as 'smart' | 'domain' | 'content' | 'hybrid',
        preserveExistingFolders: options.preserveExistingFolders,
        useAI: options.useAI
      })

      debugLog('📋 Organization result received:', {
        success: result.success,
        bookmarksCount: result.organizedBookmarks?.length || 0,
        foldersCreated: result.foldersCreated?.length || 0,
        message: result.summary
      });

      if (result.success) {
        console.log('🎯 Organization result received:', {
          success: result.success,
          bookmarksCount: result.organizedBookmarks.length,
          foldersCreated: result.foldersCreated?.length || 0,
          summary: result.summary // Use summary instead of message
        });

        // Debug: Check a sample of organized bookmarks
        const sampleBookmarks = result.organizedBookmarks.slice(0, 5);
        console.log('🎯 Sample organized bookmarks:', sampleBookmarks.map(b => ({
          title: b.title,
          originalCollection: b.collection,
          url: b.url
        })));

        // Update the bookmarks with the organized structure and rebuild collections
        setBookmarkData(prevData => {
          debugLog('🎯 BEFORE UPDATE - Collections:', prevData.collections.length, 'Bookmarks:', prevData.bookmarks.length);

          // Rebuild collections from the organized bookmarks
          const collectionsMap = new Map<string, number>()

          result.organizedBookmarks.forEach((bookmark, index) => {
            const collection = bookmark.collection || 'Uncategorized'
            const count = collectionsMap.get(collection) || 0
            collectionsMap.set(collection, count + 1)

            // Log first few bookmark updates
            if (index < 5) {
              debugLog(`🔄 Bookmark ${index + 1}: "${bookmark.title}" → Collection: "${collection}"`);
            }
          })

          debugLog('🗂️ Collections map built with', collectionsMap.size, 'unique collections');

          console.log('🎯 Collections map built:', Array.from(collectionsMap.entries()).slice(0, 10));

          const updatedCollections: Collection[] = Array.from(collectionsMap.entries())
            .filter(([name, _count]) => name && name.trim() !== '') // ZERO FILTERING: Only filter out truly empty names, preserve all collections including count=0
            .map(([name, count]) => ({
              id: name.toLowerCase().replace(/\s+/g, '-'),
              name: name.trim(),
              color: getCollectionColorForContext(name),
              count // Preserve count even if 0 - empty collections are valid
            }))

          // Remove duplicates by ID
          const uniqueCollections = updatedCollections.filter((collection, index, self) =>
            index === self.findIndex(c => c.id === collection.id)
          );

          debugLog('✅ FINAL RESULT:');
          debugLog('📊 Unique collections created:', uniqueCollections.length);
          debugLog('📊 Collections map size:', collectionsMap.size);
          debugLog('📊 Sample collections:', uniqueCollections.slice(0, 5).map(c => `${c.name} (${c.count})`));

          const updatedData = {
            ...prevData,
            bookmarks: result.organizedBookmarks,
            collections: uniqueCollections
          }

          debugLog('💾 Auto-saving organized structure...');
          // Auto-save the organized structure
          autoSave(updatedData)

          // Force a re-render to ensure UI updates
          setTimeout(() => {
            debugLog('🔄 AFTER TIMEOUT - Final collections count:', updatedData.collections.length);
            debugLog('🔄 Window debug info:', (window as any).lastSmartAIDebug);
          }, 100);

          debugLog('🎯 RETURNING UPDATED DATA with', updatedData.collections.length, 'collections');
          return updatedData
        })
      }

      return {
        success: result.success,
        summary: result.summary,
        foldersCreated: result.foldersCreated,
        bookmarksMoved: result.bookmarksMoved
      }
    } catch (error) {
      const originalConsoleError = console.error;
      originalConsoleError('🔥 SMART AI ERROR:', error);
      originalConsoleError('🔥 Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });

      return {
        success: false,
        summary: 'Organization failed due to an error',
        foldersCreated: [],
        bookmarksMoved: 0
      }
    }
  }

  const previewAutoOrganize = async (options: { strategy: string, preserveExistingFolders: boolean, useAI: boolean }) => {
    try {
      // Import the service dynamically to avoid circular dependencies
      const { autoOrganizeService } = await import('../../services/autoOrganizeService')

      const preview = await autoOrganizeService.previewOrganization(bookmarkData.bookmarks, {
        strategy: options.strategy as 'smart' | 'domain' | 'content' | 'hybrid',
        preserveExistingFolders: options.preserveExistingFolders,
        useAI: options.useAI
      })

      return preview
    } catch (error) {
      console.error('Preview failed:', error)
      return {
        foldersToCreate: [],
        bookmarksToMove: [],
        summary: 'Preview failed due to an error'
      }
    }
  }

  const value: BookmarkContextType = {
    bookmarks: bookmarkData.bookmarks,
    collections: bookmarkData.collections,
    tags: bookmarkData.tags,
    playlists: bookmarkData.playlists,
    filteredBookmarks,
    selectedBookmarks,
    searchQuery,
    searchOptions,
    selectedCollection,
    selectedTags,
    selectedPlaylist,
    filterType,
    isLoading,
    isSelectMode,
    setSearchQuery,
    setSearchOptions,
    setSelectedCollection,
    setSelectedTags,
    setSelectedPlaylist,
    setFilterType,
    toggleBookmarkFavorite,
    deleteBookmark,
    addBookmark,
    addBookmarksBulk,
    updateBookmark,
    incrementVisitCount,
    clearAllBookmarks,
    replaceAllBookmarks,
    importBookmarks,
    toggleSelectMode,
    toggleBookmarkSelection,
    selectAllBookmarks,
    deselectAllBookmarks,
    deleteSelectedBookmarks,
    addSelectedToPlaylist,
    exportBookmarks,
    splitBookmarks,
    // Playlist functions
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    addBookmarkToPlaylist,
    removeBookmarkFromPlaylist,
    getPlaylistBookmarks,
    // Drag & Drop functions
    addBookmarkFromDrop,
    revertRecentBookmark,
    clearRecentStatus,
    // Save functions
    saveBookmarks,
    hasUnsavedChanges,
    autoSaveEnabled,
    setAutoSaveEnabled,
    // Auto-organize functions
    autoOrganizeBookmarks,
    previewAutoOrganize,
    // File attachment
    attachedFile,
    saveMode,
    setSaveMode,
    detachFile,
    // Cache management
    clearCacheAndRefresh
  }

  return (
    <BookmarkContext.Provider value={value}>
      {children}
    </BookmarkContext.Provider>
  )
}
