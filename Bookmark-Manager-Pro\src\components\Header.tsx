import {
  Check,
  Download,
  Home,
  ListMusic,
  Palette,
  SplitSquareVertical,
  Upload,
  X
} from 'lucide-react'
import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useBookmarks } from '../contexts/BookmarkContext'
import AdvancedSearch from './AdvancedSearch'
// import { QuickModernThemeSwitch } from './ModernThemeToggle'
import { ThemeToggle } from './ThemeToggle'

// Standardized button component for consistent styling
const HeaderButton: React.FC<{
  onClick: () => void
  variant?: 'primary' | 'secondary' | 'icon'
  children: React.ReactNode
  'aria-label': string
  title?: string
  disabled?: boolean
  active?: boolean
}> = ({
  onClick,
  variant = 'secondary',
  children,
  'aria-label': ariaLabel,
  title,
  disabled = false,
  active = false
}) => {
    const baseStyles = {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      border: 'none',
      cursor: disabled ? 'not-allowed' : 'pointer',
      borderRadius: 'var(--radius-md)',
      transition: 'all var(--transition-fast)',
      fontSize: '14px',
      fontWeight: '500',
      position: 'relative' as const,
      zIndex: 999,
      opacity: disabled ? 0.5 : 1,
      pointerEvents: disabled ? 'none' as const : 'auto' as const
    }

    const variantStyles = {
      primary: {
        ...baseStyles,
        gap: 'var(--padding-sm)',
        padding: '12px 20px',
        background: active ? 'var(--accent-color)' : 'var(--secondary-bg)', // More prominent active color
        border: active ? '2px solid var(--accent-color)' : '1px solid var(--border-color)',
        color: active ? 'white' : 'var(--text-secondary)',
        minWidth: '100px',
        height: '44px',
        boxShadow: active ? '0 0 0 3px rgba(0, 123, 255, 0.25)' : 'none' // Active glow
      },
      secondary: {
        ...baseStyles,
        gap: 'var(--padding-sm)',
        padding: '12px 16px',
        background: active ? 'var(--accent-color)' : 'var(--secondary-bg)',
        border: '1px solid var(--border-color)',
        color: active ? 'var(--text-primary)' : 'var(--text-secondary)',
        minWidth: '80px',
        height: '44px'
      },
      icon: {
        ...baseStyles,
        padding: '12px',
        background: active ? 'var(--accent-color)' : 'var(--secondary-bg)', // More prominent active color
        border: active ? '2px solid var(--accent-color)' : '1px solid var(--border-color)',
        color: active ? 'white' : 'var(--text-secondary)',
        width: '44px',
        height: '44px',
        boxShadow: active ? '0 0 0 3px rgba(0, 123, 255, 0.25)' : 'none' // Active glow
      }
    }

    return (
      <button
        onClick={onClick}
        style={variantStyles[variant]}
        aria-label={ariaLabel}
        title={title}
        disabled={disabled}
      >
        {children}
      </button>
    )
  }

// Filter button component for the filter group
const FilterButton: React.FC<{
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void
  active: boolean
  children: React.ReactNode
}> = ({ onClick, active, children }) => (
  <button
    onClick={onClick}
    style={{
      padding: '10px 16px',
      background: active ? 'var(--accent-color)' : 'transparent',
      border: 'none',
      color: active ? 'var(--text-primary)' : 'var(--text-secondary)',
      cursor: 'pointer',
      borderRadius: 'var(--radius-md)',
      transition: 'all var(--transition-fast)',
      fontSize: '13px',
      fontWeight: '500',
      minWidth: '60px',
      height: '36px'
    }}
  >
    {children}
  </button>
)

interface HeaderProps {
  onToggleImport: () => void
  onToggleExport: () => void
  onToggleSplit: () => void
  onTogglePlaylist: () => void
  importPanelOpen: boolean
  exportPanelOpen: boolean
  splitPanelOpen: boolean
  playlistPanelOpen: boolean
}

export const Header: React.FC<HeaderProps> = ({
  onToggleImport,
  onToggleExport,
  onToggleSplit,
  onTogglePlaylist,
  importPanelOpen,
  exportPanelOpen,
  splitPanelOpen,
  playlistPanelOpen
}) => {
  const navigate = useNavigate()
  const {
    setSearchQuery,
    filterType,
    setFilterType,
    filteredBookmarks,
    isSelectMode,
    toggleSelectMode,
    selectedBookmarks,
    selectAllBookmarks,
    deselectAllBookmarks,
    deleteSelectedBookmarks,
    selectedPlaylist,
    setSelectedCollection,
    autoSaveEnabled,
    setAutoSaveEnabled,
    clearCacheAndRefresh
  } = useBookmarks()

  // Optimized localization state - single source of truth
  const [locale, setLocale] = React.useState<'en-US' | 'en-GB'>(() => {
    return localStorage.getItem('bookmark-manager-locale') as 'en-US' | 'en-GB' || 'en-US'
  })

  // Simplified localization function
  const t = React.useCallback((key: string) => {
    const translations: Record<string, Record<string, string>> = {
      'en-US': { 'filter.favorites': 'Favorites', 'filter.all': 'All', 'filter.recent': 'Recent' },
      'en-GB': { 'filter.favorites': 'Favourites', 'filter.all': 'All', 'filter.recent': 'Recent' }
    }
    return translations[locale]?.[key] || key
  }, [locale])

  // Optimized toggle function
  const toggleLocale = React.useCallback(() => {
    const newLocale = locale === 'en-US' ? 'en-GB' : 'en-US'
    setLocale(newLocale)
    localStorage.setItem('bookmark-manager-locale', newLocale)
    // Use a more efficient event dispatch
    window.dispatchEvent(new CustomEvent('localeChanged', { detail: newLocale }))
  }, [locale])

  return (
    <header className="header">
      {/* Left: Search and Status Controls */}
      <div className="header-left">
        {!isSelectMode && (
          <AdvancedSearch
            bookmarks={filteredBookmarks}
            onSearchResults={(results) => {
              console.log('🔍 Advanced search results:', results.length)
            }}
            onSearchQuery={(query) => {
              console.log('🔍 Advanced search query changed:', query)
              setSearchQuery(query)
            }}
            placeholder="Search bookmarks"
            className="header-search"
          />
        )}
        {isSelectMode && (
          <span className="bookmark-count">
            {`${selectedBookmarks.length} selected / ${filteredBookmarks.length} total`}
          </span>
        )}
      </div>

      {/* Center: Filter and Locale Controls */}
      {!isSelectMode ? (
        <div className="header-center">

          <HeaderButton
            onClick={toggleLocale}
            variant="secondary"
            aria-label={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
            title={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
          >
            <span style={{ fontSize: '16px' }}>{locale === 'en-US' ? '🇺🇸' : '🇬🇧'}</span>
            <span>{locale === 'en-US' ? 'US' : 'UK'}</span>
          </HeaderButton>

          <div className="filter-buttons">
            <FilterButton
              onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                e.preventDefault()
                e.stopPropagation()
                console.log('🔍 Filter changed to: all')
                setFilterType('all')
              }}
              active={filterType === 'all'}
            >
              {t('filter.all')}
            </FilterButton>
            <FilterButton
              onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                e.preventDefault()
                e.stopPropagation()
                console.log('🔍 Filter changed to: favorites')
                setFilterType('favorites')
                // Clear collection selection to show all favorites
                setSelectedCollection('all')
              }}
              active={filterType === 'favorites'}
            >
              {t('filter.favorites')}
            </FilterButton>
            <FilterButton
              onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                e.preventDefault()
                e.stopPropagation()
                console.log('🔍 Filter changed to: recent')
                setFilterType('recent')
                // Clear collection selection to show all recent bookmarks
                setSelectedCollection('all')
              }}
              active={filterType === 'recent'}
            >
              {t('filter.recent')}
            </FilterButton>
          </div>
        </div>
      ) : (
        <div className="header-center">
          <span style={{ color: 'var(--text-secondary)', fontSize: '14px' }}>
            Select bookmarks to delete, export, or split
          </span>
          <HeaderButton
            onClick={selectAllBookmarks}
            variant="primary"
            aria-label={`Select all ${filteredBookmarks.length} bookmarks`}
          >
            Select All ({filteredBookmarks.length})
          </HeaderButton>
          <HeaderButton
            onClick={deselectAllBookmarks}
            variant="secondary"
            aria-label={`Deselect all ${selectedBookmarks.length} selected bookmarks`}
          >
            Deselect All ({selectedBookmarks.length})
          </HeaderButton>
        </div>
      )}

      {/* Right: Action Buttons */}
      {!isSelectMode ? (
        <div className="header-right">
          <HeaderButton
            onClick={onToggleImport}
            variant="primary"
            aria-label="Toggle import panel"
            active={importPanelOpen}
          >
            <Upload size={18} />
            Import
          </HeaderButton>

          <HeaderButton
            onClick={onToggleExport}
            variant="icon"
            aria-label="Export bookmarks"
            title="Export bookmarks"
            active={exportPanelOpen}
          >
            <Download size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={onToggleSplit}
            variant="icon"
            aria-label="Split bookmarks"
            title="Split bookmarks into collections"
            active={splitPanelOpen}
          >
            <SplitSquareVertical size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={onTogglePlaylist}
            variant="icon"
            aria-label="Manage playlists"
            title="Manage bookmark playlists"
            active={playlistPanelOpen}
          >
            <ListMusic size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={() => {
              console.log('🎯 Cache button clicked')
              clearCacheAndRefresh()
            }}
            variant="icon"
            aria-label="Clear cache and refresh"
            title="Clear cache and refresh to initial state"
          >
            🔄
          </HeaderButton>

          <HeaderButton
            onClick={() => {
              console.log('🎯 Select button clicked')
              toggleSelectMode()
            }}
            variant="icon"
            aria-label="Select bookmarks"
            title="Enter selection mode"
          >
            <Check size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={() => {
              navigate('/ui-showcase')
            }}
            variant="icon"
            aria-label="Open UI showcase"
            title="View modern UI components showcase"
          >
            <Palette size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={() => {
              window.open('./homepage.html', '_blank')
            }}
            variant="icon"
            aria-label="Open documentation homepage"
            title="Open documentation and project homepage"
          >
            <Home size={18} />
          </HeaderButton>

          {/* <QuickModernThemeSwitch /> */}
          <ThemeToggle />
        </div>
      ) : (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          flexShrink: 0,
          position: 'relative',
          zIndex: 200,
          minWidth: 'fit-content'
        }}>
          <HeaderButton
            onClick={toggleSelectMode}
            variant="icon"
            aria-label="Cancel selection"
            title="Exit selection mode"
          >
            <X size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={onToggleSplit}
            variant="icon"
            disabled={selectedBookmarks.length === 0}
            active={splitPanelOpen}
            aria-label="Split selected bookmarks"
            title={`Split ${selectedBookmarks.length} selected bookmark${selectedBookmarks.length !== 1 ? 's' : ''}`}
          >
            <SplitSquareVertical size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={onToggleExport}
            variant="icon"
            disabled={selectedBookmarks.length === 0}
            active={exportPanelOpen}
            aria-label="Export selected bookmarks"
            title={`Export ${selectedBookmarks.length} selected bookmark${selectedBookmarks.length !== 1 ? 's' : ''}`}
          >
            <Download size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={onTogglePlaylist}
            variant="icon"
            active={playlistPanelOpen || !!selectedPlaylist}
            aria-label="Save to playlist"
            title="Save selected bookmarks to playlist"
          >
            <ListMusic size={18} />
          </HeaderButton>

          <HeaderButton
            onClick={deleteSelectedBookmarks}
            variant="icon"
            disabled={selectedBookmarks.length === 0}
            aria-label="Delete selected bookmarks"
            title={`Delete ${selectedBookmarks.length} selected bookmark${selectedBookmarks.length !== 1 ? 's' : ''}`}
          >
            🗑️
          </HeaderButton>
        </div>
      )}
    </header>
  )
}
